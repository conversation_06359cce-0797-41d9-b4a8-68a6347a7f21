#!/usr/bin/env python3
"""Test runner script for session API tests."""

import subprocess
import sys
import time
from pathlib import Path


def run_command(command, description):
    """Run a command and print results."""
    print(f"\n{'=' * 60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'=' * 60}")

    start_time = time.time()
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    end_time = time.time()

    print(f"Exit code: {result.returncode}")
    print(f"Duration: {end_time - start_time:.2f} seconds")

    if result.stdout:
        print(f"\nSTDOUT:\n{result.stdout}")

    if result.stderr:
        print(f"\nSTDERR:\n{result.stderr}")

    return result.returncode == 0


def main():
    """Main test runner function."""
    print("Session API Test Runner")
    print("=" * 60)

    # Check if we're in the right directory
    if not Path("tests").exists():
        print("Error: tests directory not found. Please run from project root.")
        sys.exit(1)

    # Test commands to run
    test_commands = [
        {"command": "pytest tests/test_sessions_api.py -v", "description": "Basic Session API Tests"},
        {"command": "pytest tests/test_sessions_integration.py -v", "description": "Integration Tests"},
        {"command": "pytest tests/test_sessions_timing.py -v", "description": "Timing and Performance Tests"},
        {"command": "pytest tests/ -k 'session' --tb=short", "description": "All Session-related Tests"},
        {"command": "pytest tests/ -m 'timing' -v", "description": "Timing-specific Tests (if marked)"},
    ]

    # Run each test command
    results = []
    for test_config in test_commands:
        success = run_command(test_config["command"], test_config["description"])
        results.append((test_config["description"], success))

    # Summary
    print(f"\n{'=' * 60}")
    print("TEST SUMMARY")
    print(f"{'=' * 60}")

    for description, success in results:
        status = "PASSED" if success else "FAILED"
        print(f"{description}: {status}")

    # Overall result
    all_passed = all(success for _, success in results)
    if all_passed:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
