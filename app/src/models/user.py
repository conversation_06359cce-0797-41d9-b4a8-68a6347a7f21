"""doc."""

from uuid import uuid4

from fastapi_base.model import Base
from sqlalchemy import BOOLEAN, TIMESTAMP, UUID, Column, String, func


class User(Base):
    """Define User model."""

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, nullable=False)
    email = Column(String(255), nullable=False, unique=True)
    password = Column(String(255), nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP, server_default=func.now(), onupdate=func.current_timestamp(), nullable=False)
    is_deleted = Column(BOOLEAN, nullable=False, default=False)
