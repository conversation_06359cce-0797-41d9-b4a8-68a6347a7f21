import uuid

from fastapi_base.model import Base
from sqlalchemy import <PERSON><PERSON><PERSON>EA<PERSON>, TIMESTAMP, UUID, Column, ForeignKey, String, func
from sqlalchemy.orm import Mapped, mapped_column

from app.src.utils.common import generate_uuid


class ChatSession(Base):
    __tablename__ = "chat_sessions"
    session_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), default=generate_uuid)
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=generate_uuid)

    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )
    summary_session: Mapped[str] = mapped_column(String, nullable=False)
    summary_conversation: Mapped[str] = mapped_column(String, nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    is_deleted: Mapped[bool] = mapped_column(BOOLEAN, default=False, nullable=False)


class ChatConversation(Base):
    __tablename__ = "chat_conversations"
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=generate_uuid)
    session_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("chat_sessions.session_id", ondelete="CASCADE"), nullable=False
    )

    role: Mapped[str] = mapped_column(String(50), nullable=False)
    content: Mapped[str] = mapped_column(String, nullable=False)
    timestamp = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    is_summarized: Mapped[bool] = mapped_column(BOOLEAN, default=False, nullable=False)
    is_deleted: Mapped[bool] = mapped_column(BOOLEAN, default=False, nullable=False)
