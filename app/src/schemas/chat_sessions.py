"""Define chat session schema file."""

import uuid
from datetime import datetime
from typing import Any, Optional

from pydantic import BaseModel


class TokenPayload(BaseModel):
    """Define Token payload schema."""

    sub: Optional[str] = None
    user: Optional[dict[str, Any]] = None
    key: Optional[str] = None


class RefreshToken(BaseModel):
    """Schema define refresh access token input data."""

    refresh_token: str


class ChatSessionCreate(BaseModel):
    session_id: uuid.UUID
    user_id: uuid.UUID
    summary_session: str
    summary_conversation: str


class ChatConversationCreate(BaseModel):
    session_id: uuid.UUID
    role: str
    content: str


class ChatHistoryItem(BaseModel):
    role: str
    content: str
    timestamp: datetime


class ChatSessionResponse(BaseModel):
    session_id: uuid.UUID
    user_id: uuid.UUID
    is_deleted: bool
