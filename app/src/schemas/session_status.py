from enum import Enum
from typing import Any, Optional

from pydantic import BaseModel


class SessionStatus(Enum):
    SUCCESS = "Success"
    CLIENT_FAIL = "Fail"
    SERVER_ERROR = "Error"


class ResponseObject(BaseModel):
    data: Optional[Any] = None
    code: str
    message: str = "Success"

    class Config:
        schema_extra = {
            "example": {
                "data": {"session_id": "123e4567-e89b-12d3-a456-426614174000"},
                "code": "SESSION0000",
                "message": "Success",
            }
        }
