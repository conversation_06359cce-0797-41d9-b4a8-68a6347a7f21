"""Define Base schema."""

from datetime import datetime
from typing import Union

from pydantic import BaseModel, validator


class DateBetween(BaseModel):
    """Define DateBetween schema."""

    from_date: datetime
    to_date: datetime

    @validator("from_date", "to_date", pre=True)
    def parse_date(cls, value: Union[str, datetime]) -> datetime:  # noqa
        """Define parse_date validator."""
        if isinstance(value, str):
            return datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
        return value


class Paging(BaseModel):
    """doc."""

    offset: int = 1
    limit: int = 10
