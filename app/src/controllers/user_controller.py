"""Define crud user controller."""

import uuid
from typing import <PERSON><PERSON>

from fastapi import APIRouter, Depends
from fastapi_base.response import ResponseObject
from sqlalchemy.ext.asyncio import AsyncSession

from app.src.models import User
from app.src.schemas.user import UserCreate
from app.src.services.user_service import UserService
from app.src.utils.common import row2dict
from app.src.utils.wrapper_db_session import get_db_session

user_service = UserService()

user_routers = APIRouter()


@user_routers.get("/user")
async def read_users(
    _: User = Depends(user_service.get_current_superuser),
) -> ResponseObject:
    """Get all user."""
    # user = user_service.get_all_user()
    return ResponseObject(data="TODO", code="BE0000", message="")


@user_routers.get("/user/me")
async def read_user_me(data: Tuple[User, str] = Depends(user_service.get_current_user)) -> ResponseObject:
    """doc."""
    return ResponseObject(data=row2dict(data[0]), code="BE0000", message="")


@user_routers.get("/user/{user_id}")
async def read_user_by_id(
    user_id: uuid.UUID,
    db_session: AsyncSession = Depends(get_db_session),
    _: User = Depends(user_service.get_current_superuser),
) -> ResponseObject:
    """Get a specific user by id."""
    data = await user_service.get(db_session, user_id)
    return ResponseObject(data=row2dict(data), code="BE0000", message="")


@user_routers.post("/user")
async def create_user(
    user_create: UserCreate,
    db_session: AsyncSession = Depends(get_db_session),
    _: User = Depends(user_service.get_current_superuser),
) -> ResponseObject:
    """doc."""
    data = await user_service.create(db_session, user_create)
    return ResponseObject(data=row2dict(data), code="BE0000", message="")


@user_routers.delete("/user/{user_id}")
async def delete_user(
    user_id: uuid.UUID,
    db_session: AsyncSession = Depends(get_db_session),
    _: User = Depends(user_service.get_current_superuser),
) -> ResponseObject:
    """doc."""
    await user_service.delete(db_session, user_id)
    return ResponseObject(message="Delete User Success", code="BE0000")
