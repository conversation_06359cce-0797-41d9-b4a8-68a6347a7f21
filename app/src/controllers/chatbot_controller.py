import asyncio
import json
import os
import uuid
from tempfile import get<PERSON>p<PERSON>
from typing import Any, <PERSON><PERSON>, <PERSON><PERSON>

from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException
from fastapi.responses import FileResponse, StreamingResponse
from fastapi_base.crud.sql_async_repo import AsyncSession
from fastapi_base.response import ResponseObject

from app.src.models import User
from app.src.schemas.llm import ChatbotRequest
from app.src.services.llm_service import LLMService
from app.src.services.prompt_service import PromptService
from app.src.services.search_service import SearchService
from app.src.services.session_service import SessionService
from app.src.services.user_service import UserService
from app.src.utils.wrapper_db_session import get_db_session

# Create router
chatbot_routers = APIRouter()

# Initialize services
user_service = UserService()
search_service = SearchService()
prompt_service = PromptService()
session_service = SessionService()
llm_service = LLMService(search_service=search_service, prompt_service=prompt_service)


@chatbot_routers.post(
    "/be/chat",
    description="Handles user chat and returns response",
)
async def chatbot(
    request: ChatbotRequest,
    user: Tuple[User, str] = Depends(user_service.get_current_user),
    db_session: AsyncSession = Depends(get_db_session),
    session_id: Optional[str] = Header(None, alias="X-Session-ID"),
) -> Any:
    user_id = user[0].id
    try:
        if session_id:
            current_session_id = uuid.UUID(session_id)
        else:
            current_session_id = await session_service.create_session(db_session, user_id)
        chat_history = await session_service.get_conversation_history(db_session, current_session_id)
        summary_session = await session_service.get_summary_history_session(db_session, current_session_id)

        if llm_service.should_update_summary(chat_history):
            questions = llm_service.extract_questions(chat_history, request.question)
            summary_session = await session_service.summary_history_session(
                llm_service=llm_service,
                db_session=db_session,
                session_id=current_session_id,
                user_id=user_id,
                request=request,
                chat_messages=questions,
            )

        # Handle long chat session history
        # await session_service.handle_long_history_session(
        #     llm_service=llm_service,
        #     db_session=db_session,
        #     session_id=current_session_id,
        #     user_id=user_id,
        #     request=request,
        #     chat_history=chat_history
        # )

        tool_call_events = []

        async def on_tool_call(tool_name: str):
            tool_call_events.append({"name": tool_name})

        response_generator = llm_service.generate_stream_response(
            request=request,
            chat_history=chat_history,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            on_tool_call=on_tool_call,
        )

        async def stream_response(generator):
            yield f'message: "session_id": "{current_session_id}", "summary_session": "{summary_session}" \n'

            response_content = ""
            res = {"response": ""}
            sent_tool_calls = set()
            async for chunk in generator:
                for tool in tool_call_events:
                    tool = json.dumps(tool, sort_keys=True)
                    if tool not in sent_tool_calls:
                        yield f"event: {json.dumps({'tool_call': tool})}\n"
                        sent_tool_calls.add(tool)
                        await asyncio.sleep(1)

                response_content += chunk
                res["response"] += chunk
                result = ResponseObject(code="BE0000", message="success", data=res)
                yield f"data: {result.model_dump_json()}\n"
            yield "message: Done"

            await session_service.add_conversation(
                db_session=db_session,
                session_id=current_session_id,
                question=request.question,
                response=response_content,
            )

        return StreamingResponse(
            stream_response(response_generator),
            media_type="text/event-stream",
            headers={"X-Session-ID": str(current_session_id)},
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error chatbot: {str(e)}")

@chatbot_routers.get("/be/chat/download-excel", description="Download generated Excel")
async def download_excel(filename: str):
    file_path = os.path.join(gettempdir(), filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    return FileResponse(path=file_path, filename=filename, media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")

@chatbot_routers.get("/be/chat/download-csv", description="Download generated CSV")
async def download_csv(filename: str):
    file_path = os.path.join(gettempdir(), filename)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    return FileResponse(path=file_path, filename=filename, media_type="text/csv")


@chatbot_routers.get("/be/sessions", description="Get list sessions chat of user")
async def get_sessions(
    user: Tuple[User, str] = Depends(user_service.get_current_user),
    db_session: AsyncSession = Depends(get_db_session),
):
    user_id = user[0].id
    sessions = await session_service.get_session(db_session, user_id)
    if not sessions:
        return ResponseObject(code="SS0001", data={"error": "User has no session chat"}, message="Session not found")
    sessions = [{"session_id": str(s[0]), "summary_session": s[1], "created_at": s[2].isoformat()} for s in sessions]
    return ResponseObject(data={"sessions": sessions}, code="SS0000", message="")


@chatbot_routers.post(
    "/be/sessions/{session_id}",
    description="Get session chatbot history",
)
async def get_session_history(
    db_session: AsyncSession = Depends(get_db_session),
    session_id: Optional[str] = None,
) -> ResponseObject:
    current_session_id = uuid.UUID(session_id)
    history = await session_service.get_conversation_history(db_session, current_session_id)
    if not history:
        return ResponseObject(code="HST0002", data={"error": "No conversations in this session"}, message="")

    formatted_history = []
    for i in range(0, len(history), 2):
        if i + 1 < len(history):
            formatted_history.append(
                {
                    "question": history[i].content,
                    "response": history[i + 1].content,
                    "timestamp": history[i + 1].timestamp.isoformat(),
                }
            )

    return ResponseObject(data={"history": formatted_history}, code="HST0000", message="")


@chatbot_routers.post(
    "/be/delete_sessions/{session_id}",
    description="Delete session chatbot history",
)
async def delete_session_history(
    user: Tuple[User, str] = Depends(user_service.get_current_user),
    db_session: AsyncSession = Depends(get_db_session),
    session_id: Optional[str] = None,
) -> ResponseObject:
    user_id = user[0].id
    current_session_id = uuid.UUID(session_id)
    await session_service.delete_session(db_session, user_id, current_session_id)
    return ResponseObject(data={"delete_sessions": session_id}, code="DS0000", message="")
