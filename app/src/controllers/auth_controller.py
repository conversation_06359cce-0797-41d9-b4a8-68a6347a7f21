"""Define process for authentication."""

from typing import <PERSON>ple

from fastapi import APIRouter, Depends
from fastapi_base.response import ResponseObject
from sqlalchemy.ext.asyncio import AsyncSession
from starlette import status

from app.src.models import User
from app.src.schemas.chat_sessions import RefreshToken
from app.src.schemas.user import UserLogin
from app.src.services.auth_service import AuthService
from app.src.services.user_service import UserService
from app.src.utils.const.document import generate_doc_response, get_response
from app.src.utils.wrapper_db_session import get_db_session

user_service = UserService()
auth_service = AuthService()

auth_routers = APIRouter()


@auth_routers.post(
    "/login/access-token",
    responses={status.HTTP_200_OK: generate_doc_response(example=get_response("API_LOGIN_USER"), model=ResponseObject)},
)
async def login_email_password(
    login_data: UserLogin,
    db_session: AsyncSession = Depends(get_db_session),
) -> ResponseObject:
    """Define function login with email and password."""
    user = await user_service.authenticate(db_session, login_data.email, login_data.password)
    data = auth_service.login(
        "",
        {
            "user": {
                "user_id": str(user.id),
                "email": user.email,
            },
        },
    )
    return ResponseObject(data=data, code="AUTH0000", message="")


@auth_routers.post("/refresh-token")
def refresh_access_token(
    refresh_token: RefreshToken,
) -> ResponseObject:
    """Define function get access token from refresh token."""
    data = auth_service.refresh_access_token(refresh_token.refresh_token)
    return ResponseObject(data=data, code="AUTH0000", message="")


@auth_routers.post("/logout")
async def logout(
    db_session: AsyncSession = Depends(get_db_session),
    user_token: Tuple[User, str] = Depends(user_service.get_current_user),
) -> ResponseObject:
    """Define logout function."""
    await auth_service.logout(db_session, user_token[1])
    return ResponseObject(message="Logout Success", code="AUTH0000")
