from typing import Any

FIELDS = {}

RESPONSES = {
    "API_DETAIL": {
        "timestamp": "2023-09-15 10:20:59",
        "data": {},
        "message": "success",
        "code": "BE0000",
        "links": None,
        "relationships": None,
    },
    "API_LOGIN_USER": {
        "timestamp": "2023-09-15 10:00:36",
        "data": {
            "access_token": "token",
            "refresh_token": "token",
        },
        "message": "success",
        "code": "AUTH0000",
        "links": None,
        "relationships": None,
    },
}


def get_field(field: str) -> str:
    # Using FIELDS.get(field) for ignore error when start application,
    # But should using FIELDS[field] for fix document error as soon as
    return FIELDS[field]


def get_response(field: str) -> Any:
    # Using RESPONSE.get(field) for ignore error when start application,
    # But should using RESPONSE[field] for fix document error as soon as
    return RESPONSES[field]


def generate_doc_response(*, model: Any, example: dict[str, Any]) -> dict[str, Any]:
    return {
        "model": model,
        "content": {"application/json": {"example": example}},
    }
