import os
import time
import uuid
from datetime import datetime
from typing import Any

from fastapi_base.model import Base

from app.src.schemas.session_status import SessionStatus


def is_success_code(status_code: str) -> bool:
    """Define method check code success."""
    return status_code.endswith("0000")


def is_server_error_code(status_code: str) -> bool:
    """Define method check code server error."""
    return status_code.startswith("SERVER")


def code2status(code: str) -> str:
    if is_success_code(code):
        return SessionStatus.SUCCESS.value

    if is_server_error_code(code):
        return SessionStatus.SERVER_ERROR.value

    return SessionStatus.CLIENT_FAIL.value


def row2dict(row: Base) -> dict[str, Any]:
    d = {}
    for column in row.__table__.columns:
        d[column.name] = str(getattr(row, column.name))

    return d


def generate_uuid() -> uuid.UUID:
    return uuid.uuid5(uuid.NAMESPACE_DNS, str(datetime.now()))


def object_name_generator_s3(filename: str, suffix: str = ".jpg") -> str:
    if "." in filename:
        suffix = f".{filename.rsplit('.', 1)[1].lower()}"

    object_name = os.path.join(str(time.strftime("%Y%m%d")), f"{int(time.time())}_{uuid.uuid1()}{suffix}")
    return object_name
