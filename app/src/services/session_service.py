import uuid
from typing import List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from app.src.schemas.chat_sessions import <PERSON>t<PERSON><PERSON>ory<PERSON><PERSON>, ChatSessionResponse
from app.src.services.session.core.conversation_manager import ConversationManager
from app.src.services.session.core.session_manager import SessionManager
from app.src.services.session.managers.cleanup_manager import CleanupManager
from app.src.services.session.managers.summary_manager import SummaryManager
from app.src.services.session.managers.token_manager import TokenManager


class SessionService:
    """Main session service that orchestrates session management operations."""

    def __init__(self):
        self.session_manager = SessionManager()
        self.conversation_manager = ConversationManager()
        self.summary_manager = SummaryManager()
        self.token_manager = TokenManager()
        self.cleanup_manager = CleanupManager()

    # Core session operations
    async def create_session(self, db_session: AsyncSession, user_id: uuid.UUID) -> uuid.UUID:
        """Create a new chat session for a user."""
        return await self.session_manager.create_session(db_session, user_id)

    async def get_session(self, db_session: AsyncSession, user_id: uuid.UUID) -> Optional[ChatSessionResponse]:
        """Get session information for a user."""
        return await self.session_manager.get_session(db_session, user_id)

    async def delete_session(self, db_session: AsyncSession, user_id: uuid.UUID, session_id: uuid.UUID) -> None:
        """Delete a session for a user."""
        await self.session_manager.delete_session(db_session, user_id, session_id)

    # Conversation operations
    async def add_conversation(
        self, db_session: AsyncSession, session_id: uuid.UUID, question: str, response: str
    ) -> bool:
        """Add a conversation pair (question and response) to a session."""
        return await self.conversation_manager.add_conversation(db_session, session_id, question, response)

    async def get_conversation_history(self, db_session: AsyncSession, session_id: uuid.UUID) -> List[ChatHistoryItem]:
        """Get conversation history for a session."""
        return await self.conversation_manager.get_conversation_history(db_session, session_id)

    # Token management
    @staticmethod
    def count_tokens(text: str, model: str = "gpt-4.1-mini") -> int:
        """Count tokens in a text string."""
        return TokenManager.count_tokens(text, model)

    # Summary operations
    async def summary_history_session(
        self,
        llm_service,
        db_session: AsyncSession,
        session_id: uuid.UUID,
        user_id: uuid.UUID,
        request,
        chat_messages: list,
        mode: str = "summary_session",
    ) -> str:
        """Generate session or conversation summary."""
        if mode == "summary_session":
            return await self.summary_manager.generate_session_summary(
                llm_service, db_session, session_id, user_id, request, chat_messages
            )
        elif mode == "summary_conversation":
            return await self.summary_manager.generate_conversation_summary(
                llm_service, db_session, session_id, user_id, request, chat_messages
            )
        else:
            raise ValueError(f"Invalid summary mode: {mode}")

    async def get_summary_history_session(self, db_session: AsyncSession, session_id: uuid.UUID) -> str:
        """Get existing session summary."""
        return await self.summary_manager.get_session_summary(db_session, session_id)

    # Token and history management
    async def handle_long_history_session(
        self,
        llm_service,
        db_session: AsyncSession,
        session_id: uuid.UUID,
        user_id: uuid.UUID,
        request,
        chat_history,
    ) -> Optional[str]:
        """Handle long chat history by summarizing if token limit is exceeded."""
        if self.token_manager.should_truncate_history(chat_history, request.question):
            messages_to_summarize = self.token_manager.get_messages_to_summarize(chat_history)
            chat_messages = self.token_manager.extract_content_for_summary(messages_to_summarize)

            return await self.summary_history_session(
                llm_service=llm_service,
                db_session=db_session,
                session_id=session_id,
                user_id=user_id,
                request=request,
                chat_messages=chat_messages,
                mode="summary_conversation",
            )
        return None

    # Cleanup operations
    async def clean_old_sessions(self, db_session: AsyncSession, days_threshold: int = 30) -> int:
        """Clean up old sessions that exceed the age threshold."""
        return await self.cleanup_manager.clean_old_sessions(db_session, days_threshold)
