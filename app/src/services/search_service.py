from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

import decouple
import requests
from loguru import logger
from tavily import TavilyClient


class SearchProvider(ABC):
    @abstractmethod
    async def search(self, query: str, **kwargs) -> Dict[str, Any]:
        pass


class TavilySearchProvider(SearchProvider):
    def __init__(self, api_key: str):
        self.client = TavilyClient(api_key=api_key)

    async def search(self, query: str, **kwargs) -> Dict[str, Any]:
        try:
            params = {
                "search_depth": "advanced",
                "max_results": 5,
                "include_answer": True,
                "include_raw_content": False,
            }
            params.update(kwargs)

            result = self.client.search(query=query, **params)
            return result
        except Exception as e:
            logger.error(f"Tavily search error: {str(e)}")
            return {"error": str(e)}


class GoogleSearchProvider(SearchProvider):
    def __init__(self, api_key: str, cx: str):
        self.api_key = api_key
        self.cx = cx
        self.base_url = "https://www.googleapis.com/customsearch/v1"

    async def search(self, query: str, **kwargs) -> Dict[str, Any]:
        try:
            params = {"q": query, "key": self.api_key, "cx": self.cx, "num": kwargs.get("max_results", 5)}

            response = requests.get(self.base_url, params=params)
            response.raise_for_status()

            results = response.json()
            formatted_results = {
                "results": [
                    {"title": item.get("title", ""), "url": item.get("link", ""), "content": item.get("snippet", "")}
                    for item in results.get("items", [])
                ]
            }

            return formatted_results
        except Exception as e:
            logger.error(f"Google search error: {str(e)}")
            return {"error": str(e)}


class SearchService:
    def __init__(self, default_provider: str = "tavily"):
        self.providers: Dict[str, SearchProvider] = {}
        self.default_provider = default_provider
        self._initialize_providers()

    def _initialize_providers(self):
        tavily_api_key = decouple.config("TAVILY_API_KEY", default=None)
        if tavily_api_key:
            self.providers["tavily"] = TavilySearchProvider(tavily_api_key)

        google_api_key = decouple.config("GOOGLE_SEARCH_API_KEY", default=None)
        google_cx = decouple.config("GOOGLE_SEARCH_CX", default=None)
        if google_api_key and google_cx:
            self.providers["google"] = GoogleSearchProvider(google_api_key, google_cx)

    def get_provider(self, provider_name: Optional[str] = None) -> SearchProvider:
        provider = provider_name or self.default_provider

        if provider not in self.providers:
            available = ", ".join(self.providers.keys())
            raise ValueError(f"Search provider '{provider}' not available. Available providers: {available}")

        return self.providers[provider]

    async def search(self, query: str, provider: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        search_provider = self.get_provider(provider)
        return await search_provider.search(query, **kwargs)
