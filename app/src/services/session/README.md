# Session Service Refactoring

This directory contains the refactored session management components that break down the monolithic `SessionService` into focused, single-responsibility modules.

## Architecture Overview

The refactored session service follows a modular architecture with clear separation of concerns:

```
session/
├── core/                    # Core business logic
│   ├── session_manager.py   # Session lifecycle management
│   └── conversation_manager.py # Conversation operations
└── managers/                # Specialized utilities
    ├── token_manager.py     # Token counting and limits
    ├── summary_manager.py   # AI summarization
    └── cleanup_manager.py   # Maintenance operations
```

## Components

### Core Components

#### SessionManager (`core/session_manager.py`)
- **Responsibility**: Core session lifecycle operations
- **Methods**:
  - `create_session()` - Create new chat sessions
  - `get_session()` - Retrieve session information
  - `delete_session()` - Remove sessions
  - `get_summary_session()` - Get session summaries
  - `update_session_summary()` - Update session summaries
  - `update_conversation_summary()` - Update conversation summaries

#### ConversationManager (`core/conversation_manager.py`)
- **Responsibility**: Conversation operations within sessions
- **Methods**:
  - `add_conversation()` - Add question/response pairs
  - `get_conversation_history()` - Retrieve conversation history
  - `mark_conversations_as_summarized()` - <PERSON> conversations as processed
  - `extract_conversation_content()` - Extract content for processing
  - `format_conversation_text()` - Format conversations as text

### Manager Components

#### TokenManager (`managers/token_manager.py`)
- **Responsibility**: Token counting and history management
- **Methods**:
  - `count_tokens()` - Count tokens in text
  - `get_max_token_limit()` - Get configured token limits
  - `calculate_total_tokens()` - Calculate total tokens for history
  - `should_truncate_history()` - Check if truncation is needed
  - `get_messages_to_summarize()` - Get messages for summarization
  - `extract_content_for_summary()` - Extract content for AI processing

#### SummaryManager (`managers/summary_manager.py`)
- **Responsibility**: AI-powered summarization of sessions and conversations
- **Methods**:
  - `generate_session_summary()` - Generate session titles
  - `generate_conversation_summary()` - Summarize conversation history
  - `get_session_summary()` - Retrieve existing summaries
  - `_generate_summary()` - Internal LLM interaction

#### CleanupManager (`managers/cleanup_manager.py`)
- **Responsibility**: Maintenance and cleanup operations
- **Methods**:
  - `clean_old_sessions()` - Remove old sessions based on age threshold

## Benefits of Refactoring

### 1. Single Responsibility Principle
Each component has a clear, focused responsibility:
- Session management is separate from conversation management
- Token counting is isolated from summarization
- Cleanup operations are in their own module

### 2. Improved Testability
- Each component can be tested independently
- Mock dependencies are easier to manage
- Unit tests can focus on specific functionality

### 3. Better Error Handling
- Consistent error handling patterns across components
- Proper transaction management in database operations
- Clear error propagation

### 4. Enhanced Maintainability
- Changes to token counting don't affect session management
- New summarization strategies can be added without touching core logic
- Database operations are centralized in appropriate components

### 5. Reduced Coupling
- Components depend on abstractions, not concrete implementations
- LLM service is only used where needed (SummaryManager)
- Database repositories are encapsulated within managers

## Usage Examples

### Basic Session Operations
```python
session_service = SessionService()

# Create a new session
session_id = await session_service.create_session(db_session, user_id)

# Add conversation
await session_service.add_conversation(
    db_session, session_id, "Hello", "Hi there!"
)

# Get conversation history
history = await session_service.get_conversation_history(db_session, session_id)
```

### Token Management
```python
# Check if history needs truncation
if session_service.token_manager.should_truncate_history(history, new_question):
    # Handle long history
    summary = await session_service.handle_long_history_session(
        llm_service, db_session, session_id, user_id, request, history
    )
```

### Summarization
```python
# Generate session summary
summary = await session_service.summary_history_session(
    llm_service, db_session, session_id, user_id, request, 
    chat_messages, mode="summary_session"
)
```

## Migration Guide

The refactored `SessionService` maintains the same public API as the original, so existing code should work without changes. However, you can now access individual components for more granular control:

```python
# Old way (still works)
session_service = SessionService()
await session_service.create_session(db_session, user_id)

# New way (more granular)
session_service = SessionService()
await session_service.session_manager.create_session(db_session, user_id)
```

## Configuration

The refactored components use the same configuration as the original:
- `MAX_TOKENS_LENGTH` - Maximum token limit for conversations
- Model configurations for token counting
- Database connection settings

## Testing

Each component can be tested independently:

```python
# Test SessionManager
session_manager = SessionManager()
session_id = await session_manager.create_session(mock_db, user_id)

# Test TokenManager
token_count = TokenManager.count_tokens("Hello world")
should_truncate = TokenManager.should_truncate_history(history, question)

# Test ConversationManager
conversation_manager = ConversationManager()
success = await conversation_manager.add_conversation(
    mock_db, session_id, "Q", "A"
)
```

## Future Enhancements

The modular structure makes it easy to add new features:

1. **Caching Layer**: Add caching to `SessionManager` for frequently accessed sessions
2. **Analytics**: Add usage tracking to `ConversationManager`
3. **Advanced Summarization**: Extend `SummaryManager` with different AI models
4. **Batch Operations**: Add bulk operations to `CleanupManager`
5. **Rate Limiting**: Add rate limiting to `TokenManager`
