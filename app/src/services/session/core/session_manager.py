"""Core session management functionality."""

import uuid
from typing import <PERSON><PERSON>

from fastapi_base.error_code import Server<PERSON>rrorCode
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.src.models.chat_sessions import ChatSession
from app.src.repositories.chat_session import ChatSessionRepository
from app.src.schemas.chat_sessions import ChatSessionCreate, ChatSessionResponse


class SessionManager:
    """Manages core session operations like creation, retrieval, and deletion."""

    def __init__(self):
        self.chat_session_repository = ChatSessionRepository(ChatSession)

    async def create_session(self, db_session: AsyncSession, user_id: uuid.UUID) -> uuid.UUID:
        """Create a new chat session for a user.
        
        Args:
            db_session: Database session
            user_id: ID of the user creating the session
            
        Returns:
            UUID of the created session
            
        Raises:
            ServerErrorCode.DATABASE_ERROR: If database operation fails
        """
        try:
            session_id = str(uuid.uuid4())
            session_data = ChatSessionCreate(
                session_id=session_id, 
                user_id=user_id, 
                summary_session="", 
                summary_conversation=""
            )
            session = await self.chat_session_repository.create(db_session, obj_in=session_data)
            return session.session_id
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def get_session(self, db_session: AsyncSession, user_id: uuid.UUID) -> Optional[ChatSessionResponse]:
        """Get session information for a user.
        
        Args:
            db_session: Database session
            user_id: ID of the user
            
        Returns:
            Session response data or None if not found
            
        Raises:
            ServerErrorCode.DATABASE_ERROR: If database operation fails
        """
        try:
            return await self.chat_session_repository.get_session_by_user(db_session, user_id)
        except SQLAlchemyError as ex:
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def delete_session(self, db_session: AsyncSession, user_id: uuid.UUID, session_id: uuid.UUID) -> None:
        """Delete a session for a user.
        
        Args:
            db_session: Database session
            user_id: ID of the user
            session_id: ID of the session to delete
            
        Raises:
            ServerErrorCode.DATABASE_ERROR: If database operation fails
        """
        try:
            await self.chat_session_repository.delete_session(db_session, user_id, session_id)
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def get_summary_session(self, db_session: AsyncSession, session_id: uuid.UUID) -> Optional[str]:
        """Get session summary.
        
        Args:
            db_session: Database session
            session_id: ID of the session
            
        Returns:
            Session summary or None if not found
            
        Raises:
            ServerErrorCode.DATABASE_ERROR: If database operation fails
        """
        try:
            return await self.chat_session_repository.get_summary_session_by_user(db_session, session_id)
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def update_session_summary(
        self, 
        db_session: AsyncSession, 
        user_id: uuid.UUID, 
        session_id: uuid.UUID, 
        summary: str
    ) -> None:
        """Update session summary.
        
        Args:
            db_session: Database session
            user_id: ID of the user
            session_id: ID of the session
            summary: New summary text
            
        Raises:
            ServerErrorCode.DATABASE_ERROR: If database operation fails
        """
        try:
            await self.chat_session_repository.update_session_summary(
                db_session, user_id, session_id, summary
            )
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def update_conversation_summary(
        self, 
        db_session: AsyncSession, 
        user_id: uuid.UUID, 
        session_id: uuid.UUID, 
        summary: str
    ) -> None:
        """Update conversation summary.
        
        Args:
            db_session: Database session
            user_id: ID of the user
            session_id: ID of the session
            summary: New summary text
            
        Raises:
            ServerErrorCode.DATABASE_ERROR: If database operation fails
        """
        try:
            await self.chat_session_repository.update_session_conversation(
                db_session, user_id, session_id, summary
            )
        except SQLAlchemyError as ex:
            await db_session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)
