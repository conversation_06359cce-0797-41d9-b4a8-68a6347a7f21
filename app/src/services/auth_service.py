"""Define auth service file."""

from typing import Any, Dict, Optional

import decouple
from fastapi_base.authen.bearer import jwt_decode, jwt_encode
from fastapi_base.error_code import AuthErrorCode
from sqlalchemy.ext.asyncio import AsyncSession

from app.src.models import BlacklistToken, User
from app.src.repositories.blacklist_token import BlackListTokenRepository
from app.src.repositories.user import UserRepository
from app.src.schemas.blacklist_token import BlackListTokenCreate
from app.src.schemas.chat_sessions import TokenPayload

REFRESH_TOKEN_EXPIRE_SECONDS = int(decouple.config("REFRESH_TOKEN_EXPIRE_SECONDS", 60 * 60 * 24))


class AuthService(object):
    """Define auth service object."""

    def __init__(self) -> None:
        """Define constructor for Auth service object."""
        self.user_repository = UserRepository(User)
        self.blacklist_token_repository = BlackListTokenRepository(BlacklistToken)

    @staticmethod
    def login(val_input: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
        """Define login with username and password method."""
        access_token = jwt_encode(val_input, data)
        refresh_token = jwt_encode(val_input, data, REFRESH_TOKEN_EXPIRE_SECONDS)
        return {"access_token": access_token, "refresh_token": refresh_token}

    def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
        """Define refresh access token method."""
        try:
            token_data = jwt_decode(refresh_token)
            token_payload = TokenPayload(**token_data)
        except Exception:
            raise AuthErrorCode.INVALID_REFRESH_TOKEN.value

        if not token_payload.user:
            raise AuthErrorCode.INVALID_REFRESH_TOKEN.value

        user_data = {
            "user": {
                "user_id": token_payload.user.get("user_id"),
                "email": token_payload.user.get("email"),
            }
        }

        access_token = jwt_encode("", user_data)
        new_refresh_token = jwt_encode("", user_data, REFRESH_TOKEN_EXPIRE_SECONDS)

        return {"access_token": access_token, "refresh_token": new_refresh_token}

    async def logout(self, db_session: AsyncSession, token: str) -> None:
        """Define logout method."""
        token_data = jwt_decode(token)
        token_payload = TokenPayload(**token_data)
        if not token_payload.user:
            raise AuthErrorCode.INVALID_ACCESS_TOKEN.value
        user = await self.user_repository.get_user_by_email(db_session, token_payload.user["email"])
        if not user:
            raise AuthErrorCode.INVALID_ACCESS_TOKEN.value
        await self.blacklist_token_repository.create(db_session, obj_in=BlackListTokenCreate(token=token))
