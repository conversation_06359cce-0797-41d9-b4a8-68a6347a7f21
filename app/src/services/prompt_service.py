from pathlib import Path

from loguru import logger


class PromptService:
    def __init__(self):
        self.prompt_dir = Path(__file__).parent.parent / "data/prompts"

    async def _get_prompts(self):
        if not self.prompt_dir.exists():
            logger.warning(f"Prompt directory not found: {self.prompt_dir}")
            return

        with open(self.prompt_dir / "system_prompt.md", "r") as f:
            system_prompt = f.read()
        return system_prompt
