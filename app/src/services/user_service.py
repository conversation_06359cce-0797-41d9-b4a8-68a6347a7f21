"""Define user service file."""

import uuid
from typing import Any, <PERSON><PERSON>

from fastapi import Depends
from fastapi.security import HTTPAuthorizationCredentials
from fastapi_base.authen.basic import get_password_hash, verify_password
from fastapi_base.authen.bearer import jwt_decode, reusable_oauth2
from fastapi_base.error_code import AuthErrorCode
from jose import ExpiredSignatureError
from sqlalchemy.ext.asyncio import AsyncSession

from app.src import models
from app.src.exceptions.error_code import AuthenticationErrorCode, UserErrorCode
from app.src.repositories.blacklist_token import BlackListTokenRepository
from app.src.repositories.user import UserRepository
from app.src.schemas.chat_sessions import TokenPayload
from app.src.schemas.user import UserCreate
from app.src.utils.wrapper_db_session import get_db_session


class UserService(object):
    """Define User service object."""

    def __init__(self) -> None:
        """Define constructor for User service object."""
        self.user_repository = UserRepository(models.User)
        self.blacklist_token_repository = BlackListTokenRepository(models.BlacklistToken)

    async def get_user_by_access_token(self, db_session: AsyncSession, token: str) -> models.User:
        """Define method to get user by access token."""
        if await self.blacklist_token_repository.is_black_token(db_session, token):
            raise AuthenticationErrorCode.BLACKLIST_TOKEN.value
        try:
            payload = jwt_decode(token)
        except ExpiredSignatureError:
            raise AuthErrorCode.EXPIRED_ACCESS_TOKEN.value
        token_data = TokenPayload(**payload)
        if not token_data.user:
            raise AuthErrorCode.INVALID_ACCESS_TOKEN.value
        user = await self.user_repository.get_user_by_email(db_session, token_data.user["email"])
        if not user:
            raise AuthenticationErrorCode.USERNAME_NOT_FOUND.value
        return user

    async def get_current_user(
        self,
        db_session: AsyncSession = Depends(get_db_session),
        credentials: HTTPAuthorizationCredentials = Depends(reusable_oauth2),
    ) -> Tuple[models.User, str]:
        """Define get current user method."""
        user = await self.get_user_by_access_token(db_session, credentials.credentials)
        return user, credentials.credentials

    async def get_current_superuser(
        self,
        db_session: AsyncSession = Depends(get_db_session),
        credentials: HTTPAuthorizationCredentials = Depends(reusable_oauth2),
    ) -> models.User:
        """Define get current superuser method."""
        user = await self.get_user_by_access_token(db_session, credentials.credentials)
        # if not self.is_admin(user.id):
        #     raise AuthErrorCode.PERMISSION_DENIED.value
        return user

    @staticmethod
    def is_admin(user_id: uuid.UUID) -> bool:
        """Define checking if user is admin method."""
        return user_id == uuid.UUID("ad2522e0-c85d-5925-8ddb-b99a2eabe456")

    async def authenticate(self, db_session: AsyncSession, email: str, password: str) -> models.User:
        """Define authenticate method."""
        user = await self.user_repository.get_user_by_email(db_session, email)
        if not user:
            raise AuthenticationErrorCode.USERNAME_NOT_FOUND.value
        if not verify_password(password, str(user.password)):
            raise AuthErrorCode.INCORRECT_PASSWORD.value
        return user

    async def get(self, db_session: AsyncSession, user_id: uuid.UUID) -> Any:
        """Define remove user method."""
        user = await self.user_repository.get(db_session, user_id)
        if not user:
            raise AuthenticationErrorCode.USERNAME_NOT_FOUND.value
        return user

    async def create(self, db_session: AsyncSession, user_create: UserCreate) -> Any:
        """Define create user method."""
        if await self.user_repository.get_user_by_email(db_session, user_create.email):
            raise UserErrorCode.USER_EXISTED.value
        user_create.password = get_password_hash(user_create.password)
        user = await self.user_repository.create(db_session, obj_in=user_create)
        return user

    async def delete(self, db_session: AsyncSession, user_id: uuid.UUID) -> None:
        """Define remove user method."""
        user = await self.user_repository.get(db_session, user_id)
        if not user:
            raise AuthenticationErrorCode.USERNAME_NOT_FOUND.value
        await self.user_repository.delete(db_session, obj_id=user.id)
