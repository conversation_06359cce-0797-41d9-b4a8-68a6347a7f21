"""
Refactored LLM Service with improved modularity and separation of concerns.

This service orchestrates LLM operations using a modular architecture with
separate managers for tools, messages, and responses, following the same
pattern as the session service.
"""

from typing import Any, AsyncGenerator, Callable, List, Optional

from app.src.schemas.chat_sessions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.src.services.prompt_service import PromptService
from app.src.services.search_service import SearchService

from app.src.services.llm.core.llm_client import LLMClient
from app.src.services.llm.managers.tool_manager import ToolManager
from app.src.services.llm.managers.message_manager import MessageManager
from app.src.services.llm.managers.response_manager import ResponseManager


class LLMService:
    """
    Refactored LLM Service with improved modularity and separation of concerns.

    This service orchestrates LLM operations using a modular architecture with
    separate managers for tools, messages, and responses, following the same
    pattern as the session service.
    """

    def __init__(
        self,
        search_service: Optional[SearchService] = None,
        prompt_service: Optional[PromptService] = None
    ):
        """
        Initialize the LLM service with all required managers.

        Args:
            search_service: Search service for web search functionality
            prompt_service: Prompt service for system prompts
        """
        # Initialize core components
        self.llm_client = LLMClient()

        # Initialize managers
        self.tool_manager = ToolManager(
            search_service=search_service,
            prompt_service=prompt_service,
            llm_client=self.llm_client,
        )
        self.message_manager = MessageManager()
        self.response_manager = ResponseManager(
            llm_client=self.llm_client,
            tool_manager=self.tool_manager,
            message_manager=self.message_manager,
            prompt_service=prompt_service,
        )

    @property
    def openai_client(self):
        """
        Backward compatibility property to access the OpenAI client.

        Returns:
            The underlying OpenAI client from the LLM client wrapper
        """
        return self.llm_client.client
    
    # Core LLM operations
    async def generate_stream_response(
        self,
        request: Any,
        chat_history: List[ChatHistoryItem],
        max_tokens: int = 4096,
        temperature: float = 0,
        on_tool_call: Optional[Callable[[str], None]] = None,
    ) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response for the given request and chat history.

        This method delegates to the response manager for actual processing.

        Args:
            request: User request object
            chat_history: List of previous chat messages
            max_tokens: Maximum tokens in response
            temperature: Temperature parameter for randomness
            on_tool_call: Optional callback for tool call notifications

        Yields:
            Streaming response content

        Raises:
            BusinessException: For various API errors (via ChatbotErrorCode)
        """
        async for chunk in self.response_manager.generate_stream_response(
            request=request,
            chat_history=chat_history,
            max_tokens=max_tokens,
            temperature=temperature,
            on_tool_call=on_tool_call,
        ):
            yield chunk
    
    # Tool management operations
    def get_available_tools(self) -> List[str]:
        """Get list of available tool names."""
        return self.tool_manager.get_available_tools()

    def get_tool_configs(self) -> List[dict]:
        """Get configuration for all available tools."""
        return self.tool_manager.get_tool_configs()

    # Message and history operations
    @staticmethod
    def should_update_summary(chat_history: List[ChatHistoryItem]) -> bool:
        """Determine if chat history summary should be updated."""
        return MessageManager.should_update_summary(chat_history)

    @staticmethod
    def extract_questions(chat_history: List[ChatHistoryItem], current_question: str) -> List[str]:
        """Extract questions from chat history for analysis."""
        return MessageManager.extract_questions(chat_history, current_question)

    # Response metadata
    def get_response_metadata(self) -> dict:
        """Get metadata about response capabilities."""
        return self.response_manager.get_response_metadata()
