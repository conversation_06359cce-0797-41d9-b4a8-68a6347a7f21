"""
Excel styling utilities and constants.

This module provides common styling functions and constants for Excel files.
"""

from openpyxl.styles import <PERSON><PERSON><PERSON>, Font, PatternFill, Border, Side
from openpyxl.utils import get_column_letter


class ExcelStyles:
    """
    Container for Excel styling constants and utilities.
    """
    
    # Color fills
    LIGHT_PINK_FILL = PatternFill(start_color="FCE4EC", end_color="FCE4EC", fill_type="solid")
    LIGHT_BLUE_FILL = PatternFill(start_color="DCEEF9", end_color="DCEEF9", fill_type="solid")
    
    # Borders
    THIN_BORDER = Border(
        left=Side(style="thin", color="000000"),
        right=Side(style="thin", color="000000"),
        top=Side(style="thin", color="000000"),
        bottom=Side(style="thin", color="000000")
    )
    
    # Fonts
    BOLD_FONT = Font(bold=True)
    
    # Alignment
    CENTER_ALIGNMENT = Alignment(horizontal="center", vertical="center", wrap_text=True)


def set_cell(ws, row, col, value="", fill=None, bold=False, merge_end_col=None, merge_end_row=None):
    """
    Set cell value with styling and optional merging.
    
    Args:
        ws: Worksheet object
        row: Row number (1-based)
        col: Column number (1-based)
        value: Cell value
        fill: Fill pattern
        bold: Whether to make text bold
        merge_end_col: End column for horizontal merge
        merge_end_row: End row for vertical merge
        
    Returns:
        Cell object
    """
    end_row = merge_end_row or row
    end_col = merge_end_col or col
    
    # Handle merging
    if merge_end_col:
        ws.merge_cells(start_row=row, start_column=col, end_row=row, end_column=merge_end_col)
    if merge_end_row:
        ws.merge_cells(start_row=row, start_column=col, end_row=merge_end_row, end_column=col)
    
    # Set cell value and styling
    cell = ws.cell(row=row, column=col, value=value)
    cell.alignment = ExcelStyles.CENTER_ALIGNMENT
    cell.border = ExcelStyles.THIN_BORDER
    
    if fill:
        cell.fill = fill
    if bold:
        cell.font = ExcelStyles.BOLD_FONT
    
    # Apply borders to merged cells
    for r in range(row, end_row + 1):
        for c in range(col, end_col + 1):
            if r == row and c == col:
                continue
            temp_cell = ws.cell(row=r, column=c)
            temp_cell.border = ExcelStyles.THIN_BORDER
    
    return cell


def auto_adjust_column_widths(ws):
    """
    Automatically adjust column widths based on content.
    
    Args:
        ws: Worksheet object
    """
    for col in ws.columns:
        max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
        ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 2
