"""
Meta (Instagram/Facebook) Excel formatter.

This module handles Excel file generation for Meta advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class MetaFormatter(BaseExcelWriter):
    """
    Excel formatter for Meta (Instagram/Facebook) advertising campaigns.
    """
    
    async def write_excel_file(
        self, 
        campaign_data: Dict[str, Any], 
        output_format: Dict[str, Any], 
        file_path: str
    ) -> None:
        """
        Write Excel file for Meta advertising campaigns.
        
        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("Meta広告フォーマット")
        
        # Headers
        headers = [
            "キャンペーン名", "キャンペーンの目的", "広告セット名", "配信条件／配信ポイント", 
            "性別", "年齢", "エリア", "配置場所"
        ]
        
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        
        data = campaign_data.get('data', {})
        
        # Non-list fields
        non_list_fields = ["キャンペーン名", "キャンペーンの目的", "性別", "年齢", "エリア", "配置場所"]
        for idx, header in enumerate(headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = self.get_field_value(data.get(header, ""))
                set_cell(ws, 2, idx, value)
        
        # List fields
        list_fields = {"広告セット名": 2, "配信条件／配信ポイント": 2}
        for idx, header in enumerate(headers, start=1):
            if header in list_fields:
                values = self.get_field_value(data.get(header, []))
                for row, value in enumerate(values, start=2):
                    set_cell(ws, row, idx, value)
        
        # Link destination
        set_cell(ws, 4, 1, "リンク先", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 4, 2, "", merge_end_col=8)
        
        # Feed/Story ads
        self._write_feed_story_ads(ws, data)
        
        # Carousel ads
        self._write_carousel_ads(ws, data, output_format)
        
        # Auto width (approximate) - matching original logic
        from openpyxl.utils import get_column_letter
        for col in ws.columns:
            max_length = max(len(str(cell.value)) if cell.value else 0 for cell in col)
            ws.column_dimensions[get_column_letter(col[0].column)].width = max_length + 3

        wb.save(file_path)
    
    def _write_feed_story_ads(self, ws, data: Dict[str, Any]) -> None:
        """Write feed/story ads section."""
        feed_ad = self.get_field_value(data.get("フィード広告・ストーリー広告"), {})
        feed_ad_url = self.get_field_value(feed_ad.get("URL", ""))

        # Row 6–9: フィード広告・ストーリー広告
        set_cell(ws, 6, 1, "フィード広告・ストーリー広告 Facebook / instagram",
                fill=ExcelStyles.LIGHT_BLUE_FILL, merge_end_col=4)
        set_cell(ws, 6, 5, "文字数")

        # Headline
        feed_ad_headline = self.get_field_value(feed_ad.get("見出し", ""))
        set_cell(ws, 7, 1, "見出し（広告のみ）\n最大50文字")
        set_cell(ws, 7, 2, feed_ad_headline, merge_end_col=4)
        set_cell(ws, 7, 5, str(len(feed_ad_headline)))

        # Text
        feed_ad_text = self.get_field_value(feed_ad.get("テキスト", ""))
        set_cell(ws, 8, 1, "テキスト（最大250文字）")
        set_cell(ws, 8, 2, feed_ad_text, merge_end_col=4)
        set_cell(ws, 8, 5, str(len(feed_ad_text)))

        # URL
        set_cell(ws, 9, 1, "URL")
        set_cell(ws, 9, 2, feed_ad_url, merge_end_col=5)
    
    def _write_carousel_ads(self, ws, data: Dict[str, Any], output_format: Dict[str, Any]) -> None:
        """Write carousel ads section."""
        # カルーセル広告
        start_row = 10
        max_cards = output_format['fields']['カルーセル広告']['max_count']
        cards = self.get_field_value(data.get("カルーセル広告", {}), {})
        if "cards" in cards:
            cards = cards.get("cards", [])

        for i in range(max_cards):
            base_row = start_row + i * 5
            card_number = str(i + 1)

            set_cell(ws, base_row, 1, "カルーセル広告 Facebook / instagram",
                    fill=ExcelStyles.LIGHT_BLUE_FILL, merge_end_col=4)
            set_cell(ws, base_row, 5, "文字数", fill=ExcelStyles.LIGHT_BLUE_FILL)

            set_cell(ws, base_row + 1, 1, card_number,
                    fill=ExcelStyles.LIGHT_PINK_FILL, merge_end_col=5)

            card = cards[i] if i < len(cards) else {}
            card = {k: self.get_field_value(v) for k, v in card.items()} if isinstance(card, dict) else {
                "見出し": "",
                "説明文": "",
                "URL": ""
            }

            headline = card.get("見出し", "")
            set_cell(ws, base_row + 2, 1, "見出し（最大80文字）")
            set_cell(ws, base_row + 2, 2, headline, merge_end_col=4)
            set_cell(ws, base_row + 2, 5, str(len(headline)))

            description = card.get("説明文", "")
            set_cell(ws, base_row + 3, 1, "説明文（最大40文字）")
            set_cell(ws, base_row + 3, 2, description, merge_end_col=4)
            set_cell(ws, base_row + 3, 5, str(len(description)))

            url = card.get("URL", "")
            set_cell(ws, base_row + 4, 1, "URL")
            set_cell(ws, base_row + 4, 2, url, merge_end_col=5)
