"""
YouTube Ads Excel formatter.

This module handles Excel file generation for YouTube advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class YouTubeFormatter(BaseExcelWriter):
    """
    Excel formatter for YouTube advertising campaigns.
    """
    
    async def write_excel_file(
        self, 
        campaign_data: Dict[str, Any], 
        output_format: Dict[str, Any], 
        file_path: str
    ) -> None:
        """
        Write Excel file for YouTube advertising campaigns.
        
        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("YouTube広告フォーマット")
        
        # Headers
        headers = [
            "キャンペーン名", "広告グループ名", "配信条件", "デバイス", 
            "性別", "年齢", "エリア", "入札設定", "動画設定"
        ]
        
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        
        data = campaign_data.get('data', {})
        
        # Non-list fields
        non_list_fields = ["キャンペーン名", "デバイス", "性別", "年齢", "エリア", "入札設定", "動画設定"]
        for idx, header in enumerate(headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = self.get_field_value(data.get(header, ""))
                set_cell(ws, 2, idx, value)
        
        # List fields
        list_fields = {"広告グループ名": 2, "配信条件": 2}
        for idx, header in enumerate(headers, start=1):
            if header in list_fields:
                values = self.get_field_value(data.get(header, []))
                values = self.ensure_list(values, max_items=2)
                for row, value in enumerate(values, start=2):
                    set_cell(ws, row, idx, value)
        
        # Video link destination
        set_cell(ws, 4, 1, "動画リンク先", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 4, 2, "", merge_end_col=9)
        
        # Ad type section
        set_cell(ws, 6, 1, "広告種類", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 6, 2, "広告見出し", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 6, 3, "説明文", merge_end_col=7, fill=ExcelStyles.LIGHT_BLUE_FILL)
        
        # Ad type
        ad_type = self.get_field_value(data.get("広告種類", "スキップ可能なインストリーム広告"))
        set_cell(ws, 7, 1, ad_type, merge_end_row=8)
        
        # Ad headline
        ad_headline = self.get_field_value(data.get("広告見出し", ""))
        set_cell(ws, 7, 2, ad_headline, merge_end_row=8)
        
        # Descriptions
        ad_descriptions = self.get_field_value(data.get("説明文", []))
        ad_descriptions = self.ensure_list(ad_descriptions, max_items=2)
        for idx, ad_description in enumerate(ad_descriptions, start=7):
            set_cell(ws, idx, 3, ad_description, merge_end_col=7)
        
        # Video URL and destination URL
        set_cell(ws, 9, 2, "動画URL", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 9, 3, "", merge_end_col=7)
        
        set_cell(ws, 10, 2, "配信先URL", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 10, 3, "", merge_end_col=7)
        
        self.save_workbook(wb, file_path)
