"""
LINE Ads Excel formatter.

This module handles Excel file generation for LINE advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class LineFormatter(BaseExcelWriter):
    """
    Excel formatter for LINE advertising campaigns.
    """
    
    async def write_excel_file(
        self, 
        campaign_data: Dict[str, Any], 
        output_format: Dict[str, Any], 
        file_path: str
    ) -> None:
        """
        Write Excel file for LINE advertising campaigns.
        
        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("LINE広告フォーマット")
        
        # Headers
        headers = ["キャンペーン", "グループ", "配信条件／配信ポイント", "年齢", "性別", "エリア"]
        
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        
        data = campaign_data.get('data', {})
        
        # Write data for all fields
        for idx, header in enumerate(headers, start=1):
            value = self.get_field_value(data.get(header, ""))
            set_cell(ws, 2, idx, value)
        
        # Link destination
        set_cell(ws, 3, 1, "リンク先", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 3, 2, "", merge_end_col=6)
        
        # Ad text section
        set_cell(ws, 6, 1, "広告文", fill=ExcelStyles.LIGHT_BLUE_FILL, merge_end_col=5)
        set_cell(ws, 6, 6, "文字数", fill=ExcelStyles.LIGHT_BLUE_FILL)
        
        # Headline
        ad_headline = self.get_field_value(data.get("見出し", ""))
        set_cell(ws, 7, 1, "見出し（最大20文字）")
        set_cell(ws, 7, 2, ad_headline, merge_end_col=5)
        set_cell(ws, 7, 6, str(len(ad_headline)))
        
        # Description
        ad_description = self.get_field_value(data.get("説明文", ""))
        set_cell(ws, 8, 1, "説明文（最大75文字）")
        set_cell(ws, 8, 2, ad_description, merge_end_col=5)
        set_cell(ws, 8, 6, str(len(ad_description)))
        
        self.save_workbook(wb, file_path)
