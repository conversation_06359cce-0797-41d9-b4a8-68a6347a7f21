"""
Google Search Ads Excel formatter.

This module handles Excel file generation for Google Search advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class GoogleSearchFormatter(BaseExcelWriter):
    """
    Excel formatter for Google Search advertising campaigns.
    """
    
    async def write_excel_file(
        self,
        campaign_data: Dict[str, Any],
        output_format: Dict[str, Any],
        file_path: str
    ) -> None:
        """
        Write Excel file for Google Search advertising campaigns.

        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("検索広告フォーマット")

        data = campaign_data.get('data', {})
        platform = self.get_field_value(data.get("媒体", "Google"))

        # Table 1: Main campaign details
        table1_headers = ["媒体", "キャンペーン", "広告グループ", "メインキーワード", "掛合わせ1", "マッチタイプ",
                          "広告"]
        for idx, header in enumerate(table1_headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)

        # Table 1: Non-list fields
        non_list_fields = ["媒体", "キャンペーン"]
        for idx, header in enumerate(table1_headers, start=1):
            if header in non_list_fields:
                value = self.get_field_value(data.get(header, "Google" if header == "媒体" else ""))
                if isinstance(value, list):
                    value = value[0] if value else ""
                set_cell(ws, 2, idx, value, merge_end_row=3)

        # Table 1: List fields
        list_fields = ["広告グループ", "メインキーワード", "掛合わせ1", "マッチタイプ", "広告"]
        max_items = min(max([len(self.get_field_value(data.get(field, []))) for field in list_fields], default=0), 2)
        for idx, header in enumerate(table1_headers, start=1):
            if header in list_fields:
                values = self.get_field_value(data.get(header, []))
                for row, value in enumerate(values[:max_items], start=2):
                    set_cell(ws, row, idx, value)

        # Table 2: Ad details
        table2_headers = ["広告名", "見出し(30字以内)", "固定", "文字数", "説明文(90字以内)", "固定", "文字数", "パス",
                          "文字数"]
        for idx, header in enumerate(table2_headers, start=10):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)

        current_row = 2

        campaign_ad_name = self.get_field_value(data.get("広告名", ""))
        # 見出し (Headlines) - up to 15
        headlines = self.get_field_value(data.get("見出し", []))
        headline_fixed = self.get_field_value(data.get("見出し_固定", []))
        for i in range(min(15, len(headlines))):
            set_cell(ws, current_row + i, 11, headlines[i])  # 見出し
            set_cell(ws, current_row + i, 12, headline_fixed[i] if i < len(headline_fixed) else "")  # 固定
            set_cell(ws, current_row + i, 13, str(len(headlines[i])))  # 文字数

        set_cell(ws, current_row, 10, campaign_ad_name, merge_end_row=current_row+len(headlines) - 1)

        # 説明文 (Descriptions) - up to 4
        descriptions = self.get_field_value(data.get("説明文", []))
        desc_fixed = self.get_field_value(data.get("説明文_固定", []))
        for i in range(min(4, len(descriptions))):
            row = current_row + i
            set_cell(ws, row, 14, descriptions[i])  # 説明文
            set_cell(ws, row, 15, desc_fixed[i] if i < len(desc_fixed) else "")  # 固定
            set_cell(ws, row, 16, str(len(descriptions[i])))  # 文字数

        # パス (Paths) - up to 2
        paths = self.get_field_value(data.get("パス", []))
        for i in range(min(2, len(paths))):
            row = current_row + i
            set_cell(ws, row, 17, paths[i])
            set_cell(ws, row, 18, str(len(paths[i])))

        # Table 3: URL
        url_row = current_row + max(len(headlines[:15]), len(descriptions[:4]), len(paths[:2]))
        set_cell(ws, url_row, 10, "URL")
        set_cell(ws, url_row, 11, "")
        url_value = self.get_field_value(data.get("入稿先URL", ""))
        if platform == "Google":
            set_cell(ws, url_row + 1, 10, "入稿先URL:Google")
            set_cell(ws, url_row + 1, 11, url_value + "?utm_source=google&utm_medium=cpc")
        elif platform == "Yahoo":
            set_cell(ws, url_row + 1, 10, "入稿先URL:Yahoo")
            set_cell(ws, url_row + 1, 11, url_value + "?utm_source=yahoo&utm_medium=cpc")
        else:
            raise ValueError("Invalid platform specified. Must be 'Google' or 'Yahoo'.")

        # Table 4: Additional Info
        additional_fields = ["地域", "性別（googleのみ）", "年齢（googleのみ）", "年収（googleのみ）", "ターゲティング",
                             "備考"]
        add_info_row = url_row + 3
        set_cell(ws, add_info_row, 1, "＊このキャンペーンの注意事項", fill=ExcelStyles.LIGHT_BLUE_FILL, merge_end_col=7)

        for idx, field in enumerate(additional_fields):
            row = add_info_row + 1 + idx
            field_key = field.replace("（googleのみ）", "")
            if "googleのみ" in field and platform != "Google":
                value = ""
            else:
                value = self.get_field_value(data.get(field_key, ""))
            set_cell(ws, row, 1, field)
            set_cell(ws, row, 2, value, merge_end_col=7)

        self.save_workbook(wb, file_path)
