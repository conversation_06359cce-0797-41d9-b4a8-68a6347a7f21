"""
Excel file creation tool for LLM function calling.

This tool creates downloadable Excel files for advertising campaigns.
"""

import json
from pathlib import Path
from typing import Any, Dict

from app.src.services.llm.excel.excel_manager import ExcelManager
from app.src.services.llm.utils.prompt_generator import PromptGenerator
from app.src.services.prompt_service import PromptService
from .base_tool import BaseTool


class ExcelTool(BaseTool):
    """
    Tool for creating Excel files for advertising campaigns.
    
    This tool handles the complete workflow of Excel file generation
    including data extraction, formatting, and file creation.
    """
    
    def __init__(self, prompt_service: PromptService, llm_client):
        """
        Initialize the Excel tool.
        
        Args:
            prompt_service: Prompt service for system prompts
            llm_client: LLM client for data extraction
        """
        self.prompt_service = prompt_service
        self.llm_client = llm_client
        self.outputs_dir = Path(__file__).parent.parent.parent.parent / "data/outputs"
        self.excel_manager = ExcelManager(self.outputs_dir)
        self.prompt_generator = PromptGenerator()
    
    @property
    def name(self) -> str:
        """Return the tool name."""
        return "create_excel_file"
    
    @property
    def description(self) -> str:
        """Return the tool description."""
        return "Creates a downloadable Excel file for an advertising campaign based on the specified platform."
    
    @property
    def parameters(self) -> Dict[str, Any]:
        """Return the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "platform": {
                    "type": "string",
                    "description": (
                        "The advertising platform (e.g., LINE Ads, Meta, YouTube Ads, "
                        "Google Search Ads, Google Display Ads, Google Demand Gen Ads, P-Max)."
                    ),
                    "enum": [
                        "LINE Ads",
                        "Meta (Instagram/Facebook)",
                        "YouTube Ads",
                        "Google Search Ads",
                        "Google Display Ads",
                        "Google Demand Gen Ads",
                        "P-Max",
                        "Ad Extensions"
                    ]
                }
            },
            "required": ["platform"]
        }
    
    async def execute(self, platform: str, **kwargs) -> Dict[str, Any]:
        """
        Execute Excel file creation.

        Args:
            platform: Advertising platform name
            **kwargs: Additional parameters including conversation context

        Returns:
            Dictionary with download URL
        """
        # Check if platform is supported (matching original logic)
        if platform not in self.excel_manager.platform_mapping:
            supported_platforms = "YouTube・Meta・Google検索広告・Googleディスプレイ広告・Googleディマンドジェン広告・P-Max・LINE Ads・Ad Extensions"
            raise Exception(f"指定されたプラットフォーム「{platform}」は対応していません。以下の中から選択してください：{supported_platforms}")

        try:
            # Extract conversation context for data extraction
            conversation_context = kwargs.get('conversation_context', {})
            messages = conversation_context.get('messages', [])

            # Validate that advertising content exists in conversation history
            if not self._has_advertising_content(messages):
                raise Exception(
                    "❌ 広告コンテンツが見つかりません。Excelファイルを作成する前に、まず広告コンテンツ（見出し、説明文、キャンペーン情報など）を作成してください。\n"
                    "広告コンテンツを既に作成済みの場合は、「Excelファイルを再作成してください」とお伝えください。"
                )

            # Extract campaign data from conversation
            campaign_data = await self._extract_campaign_data(platform, messages)

            # Check for extraction errors (matching original logic)
            if "error" in campaign_data:
                raise Exception("❌ プラットフォームが不明です。対応している広告プラットフォームを指定してください。")

            # Validate extracted data has sufficient content
            if not self._has_sufficient_content(campaign_data):
                raise Exception(
                    "❌ 十分な広告コンテンツが抽出できませんでした。広告コンテンツ（見出し、説明文、キャンペーン情報など）を作成してから、再度Excelファイルの作成をお試しください。\n"
                    "既に広告コンテンツを作成済みの場合は、「Excelファイルを再作成してください」とお伝えください。"
                )

            # Create Excel file
            download_url = await self.excel_manager.create_excel_file(platform, campaign_data)

            return {"download_url": download_url}

        except json.JSONDecodeError as e:
            raise Exception(
                "❌ データの解析に失敗しました。広告コンテンツが正しく作成されていない可能性があります。\n"
                "広告コンテンツを再作成してから、もう一度Excelファイルの作成をお試しください。"
            )
        except FileNotFoundError as e:
            raise Exception(
                "❌ 必要なファイルが見つかりません。システム設定に問題がある可能性があります。\n"
                "しばらく時間をおいてから再度お試しください。"
            )
        except PermissionError as e:
            raise Exception(
                "❌ ファイル作成の権限がありません。システムの一時的な問題の可能性があります。\n"
                "しばらく時間をおいてから再度お試しください。"
            )
        except Exception as e:
            # Handle specific error types with better messages
            error_str = str(e)
            if "プラットフォーム" in error_str:
                raise e
            elif "JSON" in error_str or "解析" in error_str:
                raise Exception(
                    "❌ データの解析に失敗しました。広告コンテンツが正しく作成されていない可能性があります。\n"
                    "広告コンテンツを再作成してから、もう一度Excelファイルの作成をお試しください。"
                )
            elif "広告コンテンツ" in error_str or "コンテンツ" in error_str:
                raise e  # Re-raise content validation errors as-is
            else:
                raise Exception(
                    "❌ Excelファイル作成中にエラーが発生しました。\n"
                    "広告コンテンツが正しく作成されているか確認してください。既に作成済みの場合は、「Excelファイルを再作成してください」とお伝えください。"
                )

    async def _extract_campaign_data(self, platform: str, messages: list) -> Dict[str, Any]:
        """
        Extract campaign data from conversation messages using LLM.

        Args:
            platform: Advertising platform
            messages: Conversation messages

        Returns:
            Extracted campaign data
        """
        try:
            # Load output format for the platform
            format_file = self.excel_manager.platform_mapping.get(platform)
            if not format_file:
                raise ValueError(f"No format file found for platform: {platform}")

            output_format = await self.excel_manager._load_output_format(format_file)
            schema_fields = output_format.get("fields", {})

            # Generate extraction prompt
            schema_prompt = self.prompt_generator.generate_prompt_from_output_format(
                platform=platform,
                fields=schema_fields
            )

            # Get system prompt and enhance it (matching original logic exactly)
            system_prompt = await self.prompt_service._get_prompts()
            enhanced_prompt = (
                f"{system_prompt}\n\n{schema_prompt}\n"
                "Extract all relevant data from the conversation history to populate the format completely and logically, maximizing campaign effectiveness. "
                "Ensure all values are full, persuasive Japanese ad copy based on product context. "
                "Strictly return only a valid JSON object using this exact format:\n\n"
                "{\n  \"data\": { ... },\n  \"missing_fields\": [ ... ]\n}\n\n"
                "Do not include extra commentary, explanations, or markdown formatting. If data is missing, leave fields blank but maintain structure."
            )

            print(f"\n📥 Generated system_prompt:\n{enhanced_prompt}\n")

            # Prepare messages for extraction (matching original logic)
            extraction_messages = [{"role": "system", "content": enhanced_prompt}]

            # Add conversation history excluding system prompt and last message (matching original: *messages[1:-1])
            if messages and len(messages) > 2:
                # Skip first (system) and last (current user message) - matching original logic
                extraction_messages.extend(messages[1:-1])

            # Extract data using LLM
            response = await self.llm_client.create_chat_completion(
                messages=extraction_messages,
                max_tokens=4096,
                temperature=0
            )

            # Parse the response (matching original logic)
            campaign_data = json.loads(response.choices[0].message.content)
            return campaign_data

        except json.JSONDecodeError:
            # Re-raise to match original error handling
            raise json.JSONDecodeError("JSON parsing failed", "", 0)
        except Exception as e:
            # Re-raise to match original error handling
            raise e

    def _get_default_campaign_data(self, platform: str) -> Dict[str, Any]:
        """
        Get default campaign data when extraction fails.

        Args:
            platform: Advertising platform

        Returns:
            Default campaign data structure
        """
        return {
            "data": {
                "キャンペーン名": f"{platform}キャンペーン",
                "広告グループ名": ["グループ1", "グループ2"],
                "配信条件": ["条件1", "条件2"],
                "性別": "すべて",
                "年齢": "18-65",
                "エリア": "日本全国",
                "見出し": [f"{platform}の魅力的な見出し", "効果的な広告文"],
                "説明文": [f"{platform}向けの説明文", "詳細な商品説明"],
            },
            "missing_fields": []
        }

    def _has_advertising_content(self, messages: list) -> bool:
        """
        Check if the conversation history contains advertising content.

        Args:
            messages: List of conversation messages

        Returns:
            True if advertising content is found, False otherwise
        """
        if not messages or len(messages) < 2:
            return False

        # Keywords that indicate advertising content creation
        ad_content_keywords = [
            "見出し", "ヘッドライン", "headline", "タイトル",
            "説明文", "description", "広告文", "コピー",
            "キャンペーン", "campaign", "広告グループ",
            "ターゲット", "target", "配信", "予算",
            "キーワード", "keyword", "CTA", "行動喚起",
            "商品", "サービス", "ブランド", "企業",
            "広告", "ad", "プロモーション", "宣伝"
        ]

        # Check assistant messages for advertising content
        for message in messages:
            if isinstance(message, dict) and message.get("role") == "assistant":
                content = message.get("content", "").lower()
                # Check if the message contains multiple ad content keywords
                keyword_count = sum(1 for keyword in ad_content_keywords if keyword in content)
                if keyword_count >= 3:  # Require at least 3 keywords to indicate substantial ad content
                    return True

        return False

    def _has_sufficient_content(self, campaign_data: Dict[str, Any]) -> bool:
        """
        Check if the extracted campaign data has sufficient content for Excel creation.

        Args:
            campaign_data: Extracted campaign data

        Returns:
            True if sufficient content is available, False otherwise
        """
        if not campaign_data or "data" not in campaign_data:
            return False

        data = campaign_data["data"]
        if not isinstance(data, dict):
            return False

        # Check for essential fields that should have meaningful content
        essential_fields = ["見出し", "説明文", "キャンペーン名"]
        content_count = 0

        for field in essential_fields:
            field_value = data.get(field)
            if field_value:
                if isinstance(field_value, list) and len(field_value) > 0:
                    # Check if list contains non-empty strings
                    if any(str(item).strip() for item in field_value):
                        content_count += 1
                elif isinstance(field_value, str) and field_value.strip():
                    content_count += 1

        # Require at least 2 out of 3 essential fields to have content
        return content_count >= 2
    

