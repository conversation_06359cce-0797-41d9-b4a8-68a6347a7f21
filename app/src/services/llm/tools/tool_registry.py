"""
Tool registry for managing available LLM tools.

This module provides a centralized registry for all available tools
and their configurations.
"""

from typing import Any, Dict, List

from .base_tool import BaseTool


class ToolRegistry:
    """
    Registry for managing LLM tools.
    
    This class maintains a registry of available tools and provides
    methods to access them and their configurations.
    """
    
    def __init__(self):
        """Initialize the tool registry."""
        self._tools: Dict[str, BaseTool] = {}
    
    def register_tool(self, tool: BaseTool) -> None:
        """
        Register a tool in the registry.
        
        Args:
            tool: Tool instance to register
        """
        self._tools[tool.name] = tool
    
    def get_tool(self, name: str) -> BaseTool:
        """
        Get a tool by name.
        
        Args:
            name: Tool name
            
        Returns:
            Tool instance
            
        Raises:
            KeyError: If tool is not found
        """
        if name not in self._tools:
            raise KeyError(f"Tool '{name}' not found in registry")
        return self._tools[name]
    
    def get_tools_config(self) -> List[Dict[str, Any]]:
        """
        Get configuration for all registered tools.
        
        Returns:
            List of tool configurations for OpenAI function calling
        """
        return [tool.get_config() for tool in self._tools.values()]
    
    def list_tools(self) -> List[str]:
        """
        List all registered tool names.
        
        Returns:
            List of tool names
        """
        return list(self._tools.keys())
