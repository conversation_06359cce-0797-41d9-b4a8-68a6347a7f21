"""
Prompt generation utilities for LLM operations.

This module provides utilities for generating prompts from output formats
and other prompt-related operations.
"""

from typing import Dict, Any, List


class PromptGenerator:
    """
    Utility class for generating prompts from output formats.
    """
    
    def generate_prompt_from_output_format(self, platform: str, fields: Dict[str, Any]) -> str:
        """
        Generate a prompt from output format configuration.
        
        Args:
            platform: Platform name
            fields: Fields configuration from output format
            
        Returns:
            Generated prompt string
        """
        lines = self._parse_fields(fields)
        
        prompt = f"""🎯 Please generate campaign data for {platform} in the following JSON structure.

    Ensure all required fields are included and each field respects the max_chars / max_count constraints.
    Values must be realistic, persuasive Japanese ad copy derived from product context or user input.

    Format summary:
    {chr(10).join(lines)}

    Return only a valid JSON object with the following keys:
    {{
    "data": {{ ... }},
    "missing_fields": [ "フィード広告・見出し", "カルーセル広告.cards[1].URL" ]
    }}

    If any field is incomplete or missing, leave its value as "" and include the full path in missing_fields.
    """
        return prompt
    
    def _parse_fields(self, schema: Dict[str, Any], indent: int = 0) -> List[str]:
        """
        Parse fields schema into formatted lines.

        Args:
            schema: Fields schema dictionary
            indent: Current indentation level

        Returns:
            List of formatted field description lines
        """
        lines = []
        for field_name, rule in schema.items():
            if isinstance(rule, dict) and "cards" in rule:
                lines.append(
                    "  " * indent + f'- "{field_name}": Object with cards (max_count: {rule.get("max_count", 4)}):'
                )
                for subfield, subrule in rule["cards"].items():
                    lines.append(self._format_field_line(subfield, subrule, indent + 1))
            elif isinstance(rule, dict) and "subfields" in rule:
                # Handle ad extensions format with subfields
                lines.append(self._format_field_line(field_name, rule, indent))
                if rule["subfields"]:
                    lines.extend(self._parse_fields(rule["subfields"], indent + 1))
            elif isinstance(rule, dict) and any(isinstance(v, dict) for v in rule.values()):
                lines.append("  " * indent + f'- "{field_name}": Object with subfields:')
                lines.extend(self._parse_fields(rule, indent + 1))
            else:
                lines.append(self._format_field_line(field_name, rule, indent))
        return lines
    
    def _format_field_line(self, field_name: str, rules: Dict[str, Any], indent: int = 0) -> str:
        """
        Format a single field line with its rules.

        Args:
            field_name: Name of the field
            rules: Field rules dictionary
            indent: Indentation level

        Returns:
            Formatted field description line
        """
        space = "  " * indent
        description = f'{space}- "{field_name}": '

        # Add the description from the JSON if it exists
        if "description" in rules:
            description += f"{rules['description']} — "

        # Handle fields with subfields (ad extensions format)
        if "subfields" in rules:
            if "max_count" in rules:
                description += f"Array of {rules['max_count']} objects with subfields"
            else:
                description += "Object with subfields"
        # Add the format/type information
        elif "options" in rules and "max_count" in rules:
            description += f"List of {rules['max_count']} items and each value is One of {rules['options']}"
        elif "options" in rules:
            description += f"One of {rules['options']}"
        elif "max_count" in rules and isinstance(rules.get("max_count"), int):
            max_chars = rules.get("max_chars")
            item_info = f"each ≤ {max_chars} chars" if max_chars else ""
            description += f"List of {rules['max_count']} items ({item_info})"
        elif "max_chars" in rules:
            description += f"String (max {rules['max_chars']} chars)"
        else:
            description += "String"

        # Add "Required" if applicable
        if rules.get("required"):
            description += " — Required"

        return description
