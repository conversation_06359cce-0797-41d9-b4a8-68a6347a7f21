from fastapi.openapi.utils import get_openapi
from fastapi_base.app import app
from fastapi_base.middleware.common_handler import timer_middleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.cors import CORSMiddleware

from app.src.controllers.auth_controller import auth_routers
from app.src.controllers.chatbot_controller import chatbot_routers
from app.src.controllers.user_controller import user_routers
from app.src.middleware.exception_handler import business_exception_handler

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

app.add_middleware(BaseHTTPMiddleware, dispatch=timer_middleware)
app.add_exception_handler(Exception, business_exception_handler)


# Custom OpenAPI
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title="Chatbot Service",
        version="0.1.0",
        description="Initial version for chatbot service.",
        routes=app.routes,
    )
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

# Register routers
prefix_v1 = "/be/v1.0"

app.include_router(auth_routers, tags=["AUTHENTICATION"], prefix=prefix_v1)
app.include_router(user_routers, tags=["USER"], prefix=prefix_v1)
app.include_router(chatbot_routers, prefix="/chat/v1.0", tags=["Chatbot API"])
