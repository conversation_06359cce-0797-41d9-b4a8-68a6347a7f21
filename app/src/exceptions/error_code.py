"""doc."""

from enum import Enum

from fastapi_base.exception import BusinessException
from fastapi_base.response import ExceptionDetail


class AuthenticationErrorCode(Enum):
    """doc."""

    USERNAME_NOT_FOUND = BusinessException(ExceptionDetail(message="User Not Found", code="AUTH0010"))
    EMAIL_NOT_CONFIRM = BusinessException(ExceptionDetail(message="User Not Confirmed", code="AUTH0011"))
    BLACKLIST_TOKEN = BusinessException(ExceptionDetail(message="Blacklisted Access Token", code="AUTH0012"))
    INVALID_ENCRYPTION_KEY = BusinessException(ExceptionDetail(message="Invalid Public Key", code="AUTH0013"))
    INVALID_SIGNATURE = BusinessException(ExceptionDetail(message="Invalid Signature", code="AUTH0014"))
    TOKEN_NOT_FOUND = BusinessException(ExceptionDetail(message="Token Not Found", code="AUTH0015"))
    INCORRECT_SECRET_KEY = BusinessException(ExceptionDetail(message="Incorrect Key", code="AUTH0016"))


class UserErrorCode(Enum):
    """doc."""

    USER_EXISTED = BusinessException(ExceptionDetail(message="Username Existed", code="AUTH0017"))


class InfoRequestErrorCode(Enum):
    """doc."""

    REQUEST_NOT_FOUND = BusinessException(ExceptionDetail(message="Request Not Found", code="BE0001"))
    RESULT_NOT_FOUND = BusinessException(ExceptionDetail(message="Result Not Found", code="BE0002"))


class ChatbotErrorCode(Enum):
    """doc."""

    RESPONSE_NOT_AVAILABLE = BusinessException(ExceptionDetail(message="Cannot Generate Response", code="CB0001"))
    REQUEST_DENIED = BusinessException(ExceptionDetail(message="Something Wrong with your request", code="CB0002"))
    AUTHENTICATION_FAILED = BusinessException(ExceptionDetail(message="Invalid or missing API key", code="CB0003"))
    INVALID_IMAGE = BusinessException(ExceptionDetail(message="Invalid Image", code="CB0004"))
