import uuid
from datetime import datetime, timedelta
from typing import List, Optional

from fastapi_base.crud.sql_async_repo import SQLAsyncRepository
from fastapi_base.error_code import ServerErrorCode
from sqlalchemy import and_, select, update
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.src.models.chat_sessions import Chat<PERSON>onversation, ChatSession


class ChatSessionRepository(SQLAsyncRepository[ChatSession]):
    async def get_session_by_user(self, session: AsyncSession, user_id: uuid.UUID) -> Optional[ChatSession]:
        try:
            result = await session.execute(
                select(self.model.session_id, self.model.summary_session, self.model.created_at)
                .where(self.model.user_id == user_id)
                .where(self.model.is_deleted.is_(False))
                .order_by(self.model.created_at.desc())
            )
            return result.all()
        except SQLAlchemyError as ex:
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def get_summary_session_by_user(self, session: AsyncSession, session_id: uuid.UUID) -> Optional[ChatSession]:
        try:
            result = await session.execute(
                select(self.model.summary_session)
                .where(self.model.session_id == session_id)
                .where(self.model.is_deleted.is_(False))
                .order_by(self.model.created_at.desc())
            )
            return result.scalars().first()
        except SQLAlchemyError as ex:
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def update_session_summary(
        self, session: AsyncSession, user_id: uuid.UUID, session_id: uuid.UUID, summary_session: str
    ):
        try:
            session_obj = await session.execute(
                select(self.model).where(self.model.session_id == session_id).where(self.model.user_id == user_id)
            )
            session_obj = session_obj.scalar_one_or_none()

            if session_obj:
                session_obj.summary_session = summary_session
                await session.commit()
        except SQLAlchemyError as ex:
            await session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def update_session_conversation(
        self, session: AsyncSession, user_id: uuid.UUID, session_id: uuid.UUID, summary_conversation: str
    ):
        try:
            session_obj = await session.execute(
                select(self.model).where(self.model.session_id == session_id).where(self.model.user_id == user_id)
            )
            session_obj = session_obj.scalar_one_or_none()

            if session_obj:
                session_obj.summary_conversation = summary_conversation
                await session.commit()
        except SQLAlchemyError as ex:
            await session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def delete_session(self, session: AsyncSession, user_id: uuid.UUID, session_id: uuid.UUID):
        try:
            session_obj = await session.execute(
                select(self.model).where(self.model.session_id == session_id).where(self.model.user_id == user_id)
            )
            session_obj = session_obj.scalar_one_or_none()

            if session_obj:
                session_obj.is_deleted = True
                await session.commit()

        except SQLAlchemyError as ex:
            await session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def clean_old_sessions(self, session: AsyncSession, days_threshold: int = 30) -> int:
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_threshold)
            stmt = (
                update(self.model)
                .where(and_(self.model.last_activity < cutoff_date, self.model.is_deleted.is_(False)))
                .values(is_deleted=True)
            )
            result = await session.execute(stmt)
            await session.commit()
            return result.rowcount
        except SQLAlchemyError as ex:
            await session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)


class ChatConversationRepository(SQLAsyncRepository[ChatConversation]):
    async def get_conversations_by_session_id(
        self, session: AsyncSession, session_id: uuid.UUID
    ) -> List[ChatConversation]:
        try:
            result = await session.execute(
                select(self.model)
                .where(self.model.session_id == session_id)
                .where(self.model.is_summarized.is_(False))
                .where(self.model.is_deleted.is_(False))
                .order_by(self.model.timestamp.asc())
            )
            return result.scalars().all()
        except SQLAlchemyError as ex:
            raise ServerErrorCode.DATABASE_ERROR.value(ex)

    async def update_conversation_summary(self, session: AsyncSession, session_id: uuid.UUID):
        try:
            result = await session.execute(select(self.model).where(self.model.session_id == session_id))
            session_records = result.scalars().all()
            if not session_records:
                return

            for record in session_records[:-10]:
                record.is_summarized = True
            await session.commit()
            for record in session_records:
                await session.refresh(record)

        except SQLAlchemyError as ex:
            await session.rollback()
            raise ServerErrorCode.DATABASE_ERROR.value(ex)
