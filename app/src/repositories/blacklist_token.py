"""doc."""

from fastapi_base.crud.sql_async_repo import SQLAsyncRepository
from fastapi_base.error_code import Server<PERSON>rrorCode
from loguru import logger
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from app.src import models


class BlackListTokenRepository(SQLAsyncRepository[models.BlacklistToken]):
    """doc."""

    async def is_black_token(self, session: AsyncSession, token: str) -> bool:
        """Define method check black list token."""
        try:
            if (await session.scalars(select(self.model).filter(self.model.token == token))).first():
                return True
        except SQLAlchemyError as ex:
            raise ServerErrorCode.DATABASE_ERROR.value(ex)
        logger.debug(f"Get token has token={token} from table BLACKLIST_TOKENS done")
        return False
