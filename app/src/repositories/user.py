"""doc."""

from typing import Any, Optional

from fastapi_base.crud.sql_async_repo import SQLAsyncRepository
from fastapi_base.error_code import ServerErrorCode
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.src import models


class UserRepository(SQLAsyncRepository[models.User]):
    """Define User repository."""

    async def get_user_by_email(self, session: AsyncSession, value: Any) -> Optional[models.User]:
        """Define method get user by email."""
        try:
            obj = (
                await session.scalars(
                    select(self.model).where(self.model.email == value).where(self.model.is_deleted.is_(False)),
                )
            ).first()
        except Exception as ex:
            raise ServerErrorCode.DATABASE_ERROR.value(ex)
        return obj
