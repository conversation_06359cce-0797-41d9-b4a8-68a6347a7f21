import sys

import decouple
import uvicorn
from fastapi_base.logger import configure_logger, get_uvicorn_configure_logger

# from fastapi_base.logger.filter import HealthCheckFilter
from fastapi_base.logger.formatter import DEFAULT_FORMATTER

# from fastapi_base.logger.handler.file_handler import <PERSON><PERSON><PERSON><PERSON>
from fastapi_base.logger.handler.stdout_handler import StdoutHandler

decouple.config = decouple.Config(decouple.RepositoryEnv(sys.argv[1]))

configure_logger(
    handlers=[
        (
            "builtin",
            StdoutHandler(
                level=decouple.config("LOG_LEVEL"),
                log_format=DEFAULT_FORMATTER,
            ),
        ),
        # ("builtin", FileHandler(log_format=DEFAULT_FORMATTER, log_filter=HealthCheckFilter())),
    ],
)

if __name__ == "__main__":
    port = int(decouple.config("PORT"))
    workers = int(decouple.config("WORKERS"))
    host = decouple.config("HOST", "0.0.0.0")
    uvicorn.run(
        "src.app:app",
        host=host,
        port=port,
        workers=workers,
        reload=True,
        log_config=get_uvicorn_configure_logger(),
    )
