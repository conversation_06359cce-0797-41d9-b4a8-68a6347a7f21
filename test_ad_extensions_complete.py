#!/usr/bin/env python3
"""
Complete test for Ad Extensions Excel creation workflow.
"""

import asyncio
import sys
import tempfile
import os

# Add the app directory to the path
sys.path.append('/home/<USER>/Documents/Project/our-ad/our-ad-ai-be')

async def test_excel_manager():
    """Test ExcelManager with ad extensions."""
    print("🧪 Testing ExcelManager with Ad Extensions")
    print("=" * 50)
    
    try:
        from pathlib import Path
        from app.src.services.llm.excel.excel_manager import ExcelManager
        
        # Initialize excel manager
        outputs_dir = Path('/home/<USER>/Documents/Project/our-ad/our-ad-ai-be/app/src/data/outputs')
        excel_manager = ExcelManager(outputs_dir)
        print("✅ ExcelManager initialized")
        
        # Check if Ad Extensions is supported
        if "Ad Extensions" not in excel_manager.formatters:
            print("❌ Ad Extensions not found in formatters")
            return False
        
        if "Ad Extensions" not in excel_manager.platform_mapping:
            print("❌ Ad Extensions not found in platform mapping")
            return False
        
        print("✅ Ad Extensions platform is properly configured")
        
        # Test campaign data
        campaign_data = {
            "data": {
                "platform_selected": "Google",
                "サイトリンク": [
                    {
                        "見出し": "サイトリンク1",
                        "説明文1": "説明文1-1",
                        "説明文2": "説明文1-2",
                        "URL": "https://example.com/link1"
                    },
                    {
                        "見出し": "サイトリンク2",
                        "説明文1": "説明文2-1",
                        "説明文2": "説明文2-2",
                        "URL": "https://example.com/link2"
                    }
                ],
                "コールアウト": [
                    "コールアウト1",
                    "コールアウト2",
                    "コールアウト3"
                ],
                "構造化スニペット": {
                    "ヘッダー": "スタイル",
                    "値": ["値1", "値2", "値3"]
                },
                "電話番号表示": "03-1234-5678",
                "価格表示オプション": [
                    {
                        "ヘッダー": "基本プラン",
                        "説明文": "基本的なサービス",
                        "価格": "1000",
                        "単位": "円",
                        "URL": "https://example.com/basic"
                    }
                ],
                "プロモーション": {
                    "年間行事": "年末セール",
                    "プロモーションタイプ": "20",
                    "アイテム": "全商品",
                    "最終ページURL": "https://example.com/sale",
                    "プロモーションの詳細": "年末特別セール",
                    "開始日": "2024-12-01",
                    "終了日": "2024-12-31",
                    "スケジュールの詳細": {
                        "開始日": "2024-12-01",
                        "終了日": "2024-12-31",
                        "曜日と時間帯": "毎日 9:00-18:00"
                    }
                }
            },
            "missing_fields": []
        }
        
        print("✅ Test campaign data prepared")
        
        # Create Excel file
        download_url = await excel_manager.create_excel_file("Ad Extensions", campaign_data)
        print(f"✅ Excel file created successfully!")
        print(f"   Download URL: {download_url}")
        
        # Extract filename from URL and check if file exists
        filename = download_url.split("filename=")[1]
        file_path = f"/tmp/{filename}"
        
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ File exists with size: {size} bytes")
            # Clean up
            os.unlink(file_path)
            print("🧹 Cleaned up temporary file")
        else:
            print("⚠️  File not found at expected location, but creation succeeded")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_prompt_generator_integration():
    """Test PromptGenerator integration with ExcelManager."""
    print("\n🔍 Testing PromptGenerator Integration")
    print("=" * 50)
    
    try:
        from pathlib import Path
        from app.src.services.llm.utils.prompt_generator import PromptGenerator
        from app.src.services.llm.excel.excel_manager import ExcelManager
        
        # Initialize components
        outputs_dir = Path('/home/<USER>/Documents/Project/our-ad/our-ad-ai-be/app/src/data/outputs')
        excel_manager = ExcelManager(outputs_dir)
        prompt_generator = PromptGenerator()
        
        # Load format and test prompt generation
        output_format = await excel_manager._load_output_format("ad_extensions_format.json")
        schema_fields = output_format.get("fields", {})
        
        prompt = prompt_generator.generate_prompt_from_output_format(
            platform="Ad Extensions",
            fields=schema_fields
        )
        
        print("✅ Prompt generated successfully!")
        print(f"   Prompt length: {len(prompt)} characters")
        
        # Check if prompt contains expected elements
        expected_elements = ["サイトリンク", "コールアウト", "構造化スニペット", "電話番号表示", "価格表示オプション", "プロモーション"]
        missing_elements = []
        
        for element in expected_elements:
            if element not in prompt:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"⚠️  Missing elements in prompt: {missing_elements}")
        else:
            print("✅ All expected elements found in prompt")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🧪 Complete Ad Extensions Excel Creation Test")
    print("=" * 60)
    
    test1_success = await test_excel_manager()
    test2_success = await test_prompt_generator_integration()
    
    print("\n" + "=" * 60)
    if test1_success and test2_success:
        print("🎉 All tests passed! Ad Extensions Excel creation is working correctly.")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
