"""seed init data

Revision ID: 464f946cf680
Revises: 84dafe38f62c
Create Date: 2023-08-18 15:32:04.167502

"""

import uuid
from datetime import datetime
from typing import Sequence, Union

from alembic import op
from fastapi_base.authen.basic import get_password_hash
from sqlalchemy import BOOLEAN, String, column, table

# revision identifiers, used by Alembic.
from sqlalchemy.dialects.postgresql import UUID

revision: str = "464f946cf680"
down_revision: Union[str, None] = "84dafe38f62c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

user_table = table(
    "users",
    column("id", UUID(as_uuid=True)),
    column("email", String),
    column("password", String),
    column("is_deleted", BOOLEAN),
)


user_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, datetime.now().isoformat())


def upgrade() -> None:
    op.bulk_insert(
        user_table,
        [
            {
                "id": user_uuid,
                "email": "<EMAIL>",
                "password": get_password_hash("a12345678X"),
                "is_deleted": False,
            },
            {
                "id": str(uuid.uuid4()),
                "email": "<EMAIL>",
                "password": get_password_hash("a12345678X"),
                "is_deleted": False,
            },
            {
                "id": str(uuid.uuid4()),
                "email": "<EMAIL>",
                "password": get_password_hash("a12345678X"),
                "is_deleted": False,
            },
            {
                "id": str(uuid.uuid4()),
                "email": "<EMAIL>",
                "password": get_password_hash("string"),
                "is_deleted": False,
            },
            {
                "id": str(uuid.uuid4()),
                "email": "<EMAIL>",
                "password": get_password_hash("test12345"),
                "is_deleted": False,
            },
            {
                "id": str(uuid.uuid4()),
                "email": "<EMAIL>",
                "password": get_password_hash("test12345"),
                "is_deleted": False,
            },
        ],
    )


def downgrade() -> None:
    pass
