"""init database

Revision ID: 84dafe38f62c
Create Date: 2025-05-13 14:45:00.000000
"""

import uuid
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# Revision identifiers, used by Alembic.
revision: str = "84dafe38f62c"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create users table
    op.create_table(
        "users",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            default=uuid.uuid4(),
            nullable=False,
        ),
        sa.Column("email", sa.String(length=255), nullable=False, unique=True),
        sa.Column("password", sa.String(length=255), nullable=False, comment="Hashed password using bcrypt"),
        sa.Column(
            "created_at",
            sa.TIMESTAMP,
            server_default=sa.func.now(),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP,
            server_default=sa.func.now(),
            onupdate=sa.func.current_timestamp(),
            nullable=False,
        ),
        sa.Column("is_deleted", sa.BOOLEAN, nullable=False, default=False),
    )
    op.create_index(op.f("ix_users_id"), "users", ["id"], unique=True)
    op.create_index(op.f("ix_users_email"), "users", ["email"], unique=True)

    # Create blacklist token table
    op.create_table(
        "blacklist_tokens",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            default=uuid.uuid4(),
            nullable=False,
        ),
        sa.Column("token", sa.Text, nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP,
            server_default=sa.func.now(),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP,
            server_default=sa.func.now(),
            onupdate=sa.func.current_timestamp(),
            nullable=False,
        ),
        sa.Column("is_deleted", sa.BOOLEAN, nullable=False, default=False),
    )
    op.create_index(
        op.f("ix_blacklist_tokens_id"),
        "blacklist_tokens",
        ["id"],
        unique=True,
    )
    op.create_index(
        op.f("ix_blacklist_tokens_token"),
        "blacklist_tokens",
        ["token"],
        unique=True,
    )

    # Create chat_sessions table
    op.create_table(
        "chat_sessions",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            default=uuid.uuid4,
            nullable=False,
        ),
        sa.Column("user_id", UUID(as_uuid=True), sa.ForeignKey("users.id", ondelete="CASCADE"), nullable=False),
        sa.Column(
            "session_id",
            UUID(as_uuid=True),
            default=uuid.uuid4,
            nullable=False,
            unique=True,
        ),
        sa.Column("summary_session", sa.Text(), nullable=False),
        sa.Column("summary_conversation", sa.Text(), nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP,
            server_default=sa.func.now(),
            nullable=False,
        ),
        sa.Column(
            "last_activity",
            sa.TIMESTAMP,
            server_default=sa.func.now(),
            onupdate=sa.func.current_timestamp(),
            nullable=False,
        ),
        sa.Column("is_deleted", sa.Boolean(), nullable=False, server_default=sa.sql.expression.false()),
    )
    op.create_index(op.f("ix_chat_sessions_id"), "chat_sessions", ["id"], unique=True)
    op.create_index(op.f("ix_chat_sessions_session_id"), "chat_sessions", ["session_id"], unique=True)
    op.create_index(op.f("ix_chat_sessions_user_id"), "chat_sessions", ["user_id"], unique=False)

    # Create chat_conversations table
    op.create_table(
        "chat_conversations",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            default=uuid.uuid4,
            nullable=False,
        ),
        sa.Column(
            "session_id",
            UUID(as_uuid=True),
            sa.ForeignKey("chat_sessions.session_id", ondelete="CASCADE"),
            nullable=False,
        ),
        sa.Column("role", sa.String(length=50), nullable=False, comment="e.g., user, assistant"),
        sa.Column("content", sa.Text(), nullable=False),
        sa.Column(
            "timestamp",
            sa.TIMESTAMP,
            server_default=sa.func.now(),
            nullable=False,
        ),
        sa.Column("is_summarized", sa.Boolean(), nullable=False, server_default=sa.sql.expression.false()),
        sa.Column("is_deleted", sa.Boolean(), nullable=False, server_default=sa.sql.expression.false()),
    )
    op.create_index(op.f("ix_chat_conversations_id"), "chat_conversations", ["id"], unique=True)
    op.create_index(op.f("ix_chat_conversations_session_id"), "chat_conversations", ["session_id"], unique=False)


def downgrade() -> None:
    # Drop chat_conversations table
    op.drop_index(op.f("ix_chat_conversations_session_id"), table_name="chat_conversations")
    op.drop_index(op.f("ix_chat_conversations_id"), table_name="chat_conversations")
    op.drop_table("chat_conversations")

    # Drop chat_sessions table
    op.drop_index(op.f("ix_chat_sessions_user_id"), table_name="chat_sessions")
    op.drop_index(op.f("ix_chat_sessions_session_id"), table_name="chat_sessions")
    op.drop_table("chat_sessions")

    # Drop users table
    op.drop_index(op.f("ix_users_email"), table_name="users")
    op.drop_index(op.f("ix_users_id"), table_name="users")
    op.drop_table("users")

    # Drop blacklist table
    op.drop_index(op.f("ix_blacklist_tokens_id"), table_name="blacklist_tokens")
    op.drop_index(op.f("ix_blacklist_tokens_token"), table_name="blacklist_tokens")
    op.drop_table("blacklist_tokens")
