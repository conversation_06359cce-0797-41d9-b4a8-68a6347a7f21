#!/usr/bin/env python3
"""
Debug script for Ad Extensions Excel generation.
"""

import sys
import traceback

# Add the app directory to the path
sys.path.append('/home/<USER>/Documents/Project/our-ad/our-ad-ai-be')

def test_imports():
    """Test individual imports to find the issue."""
    try:
        print("Testing openpyxl import...")
        import openpyxl
        print("✅ openpyxl imported successfully")
        
        print("Testing aiofiles import...")
        import aiofiles
        print("✅ aiofiles imported successfully")
        
        print("Testing styles import...")
        from app.src.services.llm.excel.styles import ExcelStyles, set_cell
        print("✅ styles imported successfully")
        
        print("Testing base_writer import...")
        from app.src.services.llm.excel.base_writer import BaseExcelWriter
        print("✅ base_writer imported successfully")
        
        print("Testing ad_extensions_formatter import...")
        from app.src.services.llm.excel.formatters.ad_extensions_formatter import AdExtensionsFormatter
        print("✅ ad_extensions_formatter imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_formatter_creation():
    """Test creating the formatter instance."""
    try:
        from app.src.services.llm.excel.formatters.ad_extensions_formatter import AdExtensionsFormatter
        formatter = AdExtensionsFormatter()
        print("✅ AdExtensionsFormatter instance created successfully")
        return True
    except Exception as e:
        print(f"❌ Formatter creation failed: {e}")
        traceback.print_exc()
        return False

def test_excel_generation():
    """Test Excel file generation."""
    import asyncio
    import tempfile
    import os

    # Sample data
    sample_data = {
        "data": {
            "platform_selected": "Google",
            "サイトリンク": [
                {
                    "見出し": "サイトリンク1",
                    "説明文1": "説明文1-1",
                    "説明文2": "説明文1-2",
                    "URL": "https://example.com/link1"
                }
            ],
            "コールアウト": ["コールアウト1", "コールアウト2"],
            "電話番号表示": "03-1234-5678"
        }
    }

    sample_format = {"fields": {}}

    async def run_test():
        try:
            from app.src.services.llm.excel.formatters.ad_extensions_formatter import AdExtensionsFormatter
            formatter = AdExtensionsFormatter()

            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
                print(f"Creating Excel file: {tmp_file.name}")
                await formatter.write_excel_file(
                    campaign_data=sample_data,
                    output_format=sample_format,
                    file_path=tmp_file.name
                )

                if os.path.exists(tmp_file.name):
                    size = os.path.getsize(tmp_file.name)
                    print(f"✅ Excel file created successfully! Size: {size} bytes")
                    os.unlink(tmp_file.name)  # Clean up
                    return True
                else:
                    print("❌ Excel file was not created")
                    return False

        except Exception as e:
            print(f"❌ Excel generation failed: {e}")
            traceback.print_exc()
            return False

    try:
        return asyncio.run(run_test())
    except Exception as e:
        print(f"❌ Async execution failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Debugging Ad Extensions Excel Formatter")
    print("=" * 50)

    if test_imports():
        print("\n" + "-" * 30)
        if test_formatter_creation():
            print("\n" + "-" * 30)
            test_excel_generation()

    print("\n" + "=" * 50)
    print("Debug complete")
