from openpyxl import Workbook
from openpyxl.styles import Font, Alignment

wb = Workbook()
ws = wb.active
ws.title = "Meta広告フォーマット"

headers = ["キャンペーン名", "キャンペーンの目的", "広告セット名", "配信条件／配信ポイント", "性別", "年齢", "エリア", "配置場所"]
for idx, header in enumerate(headers, start=1):
    cell = ws.cell(row=1, column=idx, value=header)
    cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
    cell.font = Font(bold=True)

for col in [1, 2, 5, 6, 7, 8]:
    ws.merge_cells(start_row=2, start_column=col, end_row=3, end_column=col)

ws.cell(row=1, column=1, value="〇〇化粧品｜肌ケア｜コンバージョン")
ws.cell(row=1, column=2, value="コンバージョン / トラフィック")
ws.cell(row=2, column=3, value="乾燥肌・女性向け")
ws.cell(row=3, column=3, value="")
ws.cell(row=2, column=4, value="20代後半〜40代前半の女性、")
ws.cell(row=3, column=4, value="日本全国")
ws.cell(row=1, column=5, value="女性")
ws.cell(row=1, column=6, value="25–45")
ws.cell(row=1, column=7, value="日本全国")
ws.cell(row=1, column=8, value="Instagram\nFacebook")

ws.cell(row=4, column=1, value="リンク先")
ws.merge_cells(start_row=4, start_column=2, end_row=4, end_column=8)
ws.cell(row=4, column=2, value="https://example.com/lp")

wb.save("meta_ad_format_fixed.xlsx")