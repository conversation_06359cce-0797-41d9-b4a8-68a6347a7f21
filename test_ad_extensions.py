#!/usr/bin/env python3
"""
Test script to verify Ad Extensions Excel generation.
"""

import asyncio
import tempfile
import os
import sys

# Add the app directory to the path
sys.path.append('/home/<USER>/Documents/Project/our-ad/our-ad-ai-be')

from app.src.services.llm.excel.formatters.ad_extensions_formatter import AdExtensionsFormatter

# Sample campaign data for testing
SAMPLE_CAMPAIGN_DATA = {
    "data": {
        "platform_selected": "Google",
        "サイトリンク": [
            {
                "見出し": "サイトリンク1",
                "説明文1": "説明文1-1",
                "説明文2": "説明文1-2",
                "URL": "https://example.com/link1"
            },
            {
                "見出し": "サイトリンク2",
                "説明文1": "説明文2-1",
                "説明文2": "説明文2-2",
                "URL": "https://example.com/link2"
            }
        ],
        "コールアウト": [
            "コールアウト1",
            "コールアウト2",
            "コールアウト3"
        ],
        "構造化スニペット": {
            "ヘッダー": "スタイル",
            "値": ["値1", "値2", "値3"]
        },
        "電話番号表示": "03-1234-5678",
        "価格表示オプション": [
            {
                "ヘッダー": "基本プラン",
                "説明文": "基本的なサービス",
                "価格": "1000",
                "単位": "円",
                "URL": "https://example.com/basic"
            },
            {
                "ヘッダー": "プレミアムプラン",
                "説明文": "高品質なサービス",
                "価格": "2000",
                "単位": "円",
                "URL": "https://example.com/premium"
            }
        ],
        "プロモーション": {
            "年間行事": "年末セール",
            "プロモーションタイプ": "20",
            "アイテム": "全商品",
            "最終ページURL": "https://example.com/sale",
            "プロモーションの詳細": "年末特別セール",
            "開始日": "2024-12-01",
            "終了日": "2024-12-31",
            "スケジュールの詳細": {
                "開始日": "2024-12-01",
                "終了日": "2024-12-31",
                "曜日と時間帯": "毎日 9:00-18:00"
            }
        }
    },
    "missing_fields": []
}

SAMPLE_OUTPUT_FORMAT = {
    "fields": {
        "サイトリンク": {"type": "array", "max_count": 6},
        "コールアウト": {"type": "array", "max_count": 6}
    }
}

async def test_ad_extensions_formatter():
    """Test Ad Extensions formatter."""
    print("Testing Ad Extensions formatter...")
    formatter = AdExtensionsFormatter()
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        try:
            await formatter.write_excel_file(
                campaign_data=SAMPLE_CAMPAIGN_DATA,
                output_format=SAMPLE_OUTPUT_FORMAT,
                file_path=tmp_file.name
            )
            print(f"✅ Ad Extensions Excel file created: {tmp_file.name}")
            print(f"   File size: {os.path.getsize(tmp_file.name)} bytes")
            return True
        except Exception as e:
            print(f"❌ Ad Extensions formatter failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            # Clean up
            if os.path.exists(tmp_file.name):
                os.unlink(tmp_file.name)

async def main():
    """Run the test."""
    print("🧪 Testing Ad Extensions Excel Formatter")
    print("=" * 50)
    
    success = await test_ad_extensions_formatter()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test passed!")
        return 0
    else:
        print("💥 Test failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
