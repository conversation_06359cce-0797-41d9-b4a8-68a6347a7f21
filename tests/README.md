# Session API Tests

This directory contains comprehensive tests for the `/be/sessions` and `/be/sessions/{session_id}` API endpoints, with a focus on timing and logging functionality.

## Test Files Overview

### Core Test Files

1. **`test_sessions_api.py`** - Main API endpoint tests
   - Basic functionality tests for both endpoints
   - Authentication and authorization tests
   - Response validation tests
   - Error handling tests

2. **`test_sessions_integration.py`** - Integration tests
   - Database interaction simulation
   - Performance tests with large datasets
   - Concurrent request handling
   - Error handling with database failures

3. **`test_sessions_timing.py`** - Timing and logging focused tests
   - Response time measurement and validation
   - Performance benchmarking
   - Logging verification
   - Load testing simulation

4. **`test_utils.py`** - Test utilities and helpers
   - Test data factories
   - Authentication helpers
   - Timing measurement utilities
   - Response validators
   - Performance analysis tools

### Configuration Files

- **`conftest.py`** - Pytest fixtures and configuration
- **`pytest.ini`** - Pytest configuration
- **`README.md`** - This documentation file

## Test Coverage

### API Endpoints Tested

1. **GET `/be/sessions`**
   - ✅ Successful retrieval of user sessions
   - ✅ No sessions found scenario
   - ✅ Unauthorized access handling
   - ✅ Invalid token handling
   - ✅ Response time validation
   - ✅ Large dataset performance

2. **POST `/be/sessions/{session_id}`**
   - ✅ Successful session history retrieval
   - ✅ No conversations found scenario
   - ✅ Invalid session ID handling
   - ✅ Non-existent session handling
   - ✅ Response time validation
   - ✅ Large conversation history performance

### Timing and Performance Tests

- **Response Time Measurement**: All endpoints are tested for response time
- **Performance Benchmarking**: Multiple request performance analysis
- **Load Testing**: Simulated high-load scenarios
- **Concurrent Requests**: Multi-threaded request testing
- **Database Timing**: Simulated database operation timing

### Logging Tests

- **Request Logging**: Verification of request start/completion logging
- **Performance Logging**: Response time logging validation
- **Error Logging**: Error scenario logging verification
- **Middleware Integration**: Timer middleware integration testing

## Running the Tests

### Prerequisites

1. Install test dependencies:
   ```bash
   pip install pytest pytest-asyncio pytest-cov
   ```

2. Ensure the application dependencies are installed:
   ```bash
   pip install -r cicd/requirements/requirements.txt
   pip install -r cicd/requirements/requirements-style.txt
   ```

### Running All Tests

```bash
# Run all session-related tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=app/src/controllers/chatbot_controller --cov-report=html

# Run specific test files
pytest tests/test_sessions_api.py -v
pytest tests/test_sessions_integration.py -v
pytest tests/test_sessions_timing.py -v
```

### Running Specific Test Categories

```bash
# Run only timing tests
pytest tests/test_sessions_timing.py -v

# Run only integration tests
pytest tests/test_sessions_integration.py -v

# Run tests with specific markers (if configured)
pytest tests/ -m "timing" -v
pytest tests/ -m "integration" -v
```

### Using the Test Runner Script

```bash
# Make the script executable
chmod +x run_tests.py

# Run all tests with detailed output
python run_tests.py
```

## Test Structure and Patterns

### Test Data Creation

Tests use factory patterns for creating test data:

```python
from tests.test_utils import TestDataFactory

# Create test user data
user_data = TestDataFactory.create_user_data()

# Create test session data
session_data = TestDataFactory.create_session_data(user_id=user_data["id"])

# Create multiple sessions
sessions = TestDataFactory.create_multiple_sessions(user_data["id"], count=10)
```

### Authentication Testing

```python
from tests.test_utils import AuthTestHelper

# Create auth token and headers
token = AuthTestHelper.create_auth_token(user_data)
headers = AuthTestHelper.create_auth_headers(token)

# Test with invalid auth
invalid_headers = AuthTestHelper.create_invalid_auth_headers()
```

### Timing Measurement

```python
from tests.test_utils import TimingTestHelper

# Measure response time
response, response_time = TimingTestHelper.measure_response_time(
    client.get, "/api/endpoint", headers=headers
)

# Assert timing requirements
TimingTestHelper.assert_response_time_within_limit(response_time, 1.0)
```

### Performance Testing

```python
from tests.test_utils import PerformanceTestHelper

# Run multiple requests and analyze performance
response_times = PerformanceTestHelper.run_multiple_requests(request_func, count=10)
metrics = PerformanceTestHelper.analyze_performance_metrics(response_times)

# Assert performance requirements
PerformanceTestHelper.assert_performance_requirements(
    response_times,
    max_avg_time=0.1,
    max_single_time=0.2
)
```

## Mocking Strategy

The tests use comprehensive mocking to isolate the API layer:

- **Database Sessions**: Mocked using `AsyncMock`
- **Service Layer**: Patched service methods with controlled responses
- **Authentication**: Mocked user authentication and token validation
- **External Dependencies**: All external calls are mocked

## Expected Test Results

When running the tests, you should see:

1. **Response Time Validation**: All endpoints respond within acceptable time limits
2. **Logging Verification**: Proper logging of request timing and performance metrics
3. **Error Handling**: Graceful handling of various error scenarios
4. **Performance Metrics**: Detailed performance analysis and benchmarking

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed and the Python path includes the project root
2. **Async Test Issues**: Make sure `pytest-asyncio` is installed and configured
3. **Mock Failures**: Verify that the mocked service paths match the actual import paths in the controllers

### Debug Mode

Run tests with more verbose output:

```bash
pytest tests/ -v -s --tb=long
```

### Performance Issues

If tests are running slowly:

1. Check if any real database connections are being made
2. Verify all external services are properly mocked
3. Reduce the number of iterations in performance tests if needed

## Contributing

When adding new tests:

1. Follow the existing naming conventions
2. Use the utility classes in `test_utils.py`
3. Include timing measurements for new endpoints
4. Add appropriate logging verification
5. Update this README with new test coverage
