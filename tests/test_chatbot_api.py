import logging
import time
import uuid
from datetime import datetime
from typing import Op<PERSON>, <PERSON><PERSON>
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi import Depends, FastAPI
from httpx import AsyncClient
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    filename="api_test.log",
)
logger = logging.getLogger(__name__)


# Mock User class
class User(BaseModel):
    id: str


# Mock ResponseObject class
class ResponseObject(BaseModel):
    code: str
    data: dict
    message: str


# Mock AsyncSession
class AsyncSession:
    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


# Mock dependencies
async def get_db_session():
    return AsyncSession()


async def get_current_user():
    return (User(id=str(uuid.uuid4())), "mock_token")


# Mock session_service
session_service = MagicMock()
session_service.get_session = AsyncMock()
session_service.get_conversation_history = AsyncMock()


# Mock conversation history item
class ConversationItem:
    def __init__(self, content: str, timestamp: datetime):
        self.content = content
        self.timestamp = timestamp


# API endpoints
async def get_sessions(
    user: Tuple[User, str] = Depends(get_current_user),
    db_session: AsyncSession = Depends(get_db_session),
):
    start_time = time.time()
    user_id = user[0].id
    sessions = await session_service.get_session(db_session, user_id)
    if not sessions:
        logger.info(f"get_sessions executed in {time.time() - start_time:.4f} seconds")
        return ResponseObject(code="SS0001", data={"error": "User has no session chat"}, message="Session not found")
    sessions = [{"session_id": str(s[0]), "summary_session": s[1], "created_at": s[2].isoformat()} for s in sessions]
    logger.info(f"get_sessions executed in {time.time() - start_time:.4f} seconds")
    return ResponseObject(data={"sessions": sessions}, code="SS0000", message="")


async def get_session_history(
    db_session: AsyncSession = Depends(get_db_session),
    session_id: Optional[str] = None,
) -> ResponseObject:
    start_time = time.time()
    try:
        current_session_id = uuid.UUID(session_id)
    except ValueError:
        logger.info(f"get_session_history executed in {time.time() - start_time:.4f} seconds")
        return ResponseObject(code="HST0003", data={"error": "Invalid session ID format"}, message="Invalid UUID")
    history = await session_service.get_conversation_history(db_session, current_session_id)
    if not history:
        logger.info(f"get_session_history executed in {time.time() - start_time:.4f} seconds")
        return ResponseObject(code="HST0002", data={"error": "No conversations in this session"}, message="")
    formatted_history = []
    for i in range(0, len(history), 2):
        if i + 1 < len(history):
            formatted_history.append(
                {
                    "question": history[i].content,
                    "response": history[i + 1].content,
                    "timestamp": history[i + 1].timestamp.isoformat(),
                }
            )
    logger.info(f"get_session_history executed in {time.time() - start_time:.4f} seconds")
    return ResponseObject(data={"history": formatted_history}, code="HST0000", message="")


# Test class
@pytest.mark.asyncio
class TestChatbotAPI:
    @pytest.fixture
    def app(self):
        app = FastAPI()
        app.get("/be/sessions")(get_sessions)
        app.post("/be/sessions/{session_id}")(get_session_history)
        return app

    @pytest.fixture
    async def client(self, app):
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

    async def test_get_sessions_success(self, client):
        # Mock session_service.get_session
        mock_sessions = [
            (uuid.uuid4(), "Chat about travel plans", datetime(2025, 6, 2, 10, 0)),
            (uuid.uuid4(), "Tech support queries", datetime(2025, 6, 1, 15, 30)),
            (uuid.uuid4(), "General Q&A session", datetime(2025, 5, 31, 9, 45)),
        ]
        session_service.get_session.return_value = mock_sessions

        start_time = time.time()
        response = await client.get("/be/sessions")
        logger.info(f"test_get_sessions_success API call executed in {time.time() - start_time:.4f} seconds")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == "SS0000"
        assert "sessions" in response_data["data"]
        assert len(response_data["data"]["sessions"]) == 3
        assert response_data["data"]["sessions"][0]["summary_session"] == "Chat about travel plans"
        assert response_data["message"] == ""

    async def test_get_sessions_no_sessions(self, client):
        # Mock session_service.get_session with empty result
        session_service.get_session.return_value = []

        start_time = time.time()
        response = await client.get("/be/sessions")
        logger.info(f"test_get_sessions_no_sessions API call executed in {time.time() - start_time:.4f} seconds")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == "SS0001"
        assert response_data["data"] == {"error": "User has no session chat"}
        assert response_data["message"] == "Session not found"

    async def test_get_session_history_success(self, client):
        # Mock session_service.get_conversation_history
        mock_history = [
            ConversationItem("What is the capital of France?", datetime(2025, 6, 2, 10, 0)),
            ConversationItem("The capital of France is Paris.", datetime(2025, 6, 2, 10, 1)),
            ConversationItem("How far is Paris from London?", datetime(2025, 6, 2, 10, 2)),
            ConversationItem("It's about 344 km by road.", datetime(2025, 6, 2, 10, 3)),
            ConversationItem("What's the weather like in Paris?", datetime(2025, 6, 2, 10, 4)),
            ConversationItem("It's sunny with a high of 25°C.", datetime(2025, 6, 2, 10, 5)),
        ]
        session_service.get_conversation_history.return_value = mock_history

        session_id = str(uuid.uuid4())
        start_time = time.time()
        response = await client.post(f"/be/sessions/{session_id}")
        logger.info(f"test_get_session_history_success API call executed in {time.time() - start_time:.4f} seconds")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == "HST0000"
        assert "history" in response_data["data"]
        assert len(response_data["data"]["history"]) == 3
        assert response_data["data"]["history"][0]["question"] == "What is the capital of France?"
        assert response_data["data"]["history"][0]["response"] == "The capital of France is Paris."
        assert response_data["message"] == ""

    async def test_get_session_history_no_conversations(self, client):
        # Mock session_service.get_conversation_history with empty result
        session_service.get_conversation_history.return_value = []

        session_id = str(uuid.uuid4())
        start_time = time.time()
        response = await client.post(f"/be/sessions/{session_id}")
        logger.info(
            f"test_get_session_history_no_conversations API call executed in {time.time() - start_time:.4f} seconds"
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == "HST0002"
        assert response_data["data"] == {"error": "No conversations in this session"}
        assert response_data["message"] == ""

    async def test_get_session_history_invalid_uuid(self, client):
        session_id = "invalid-uuid"
        start_time = time.time()
        response = await client.post(f"/be/sessions/{session_id}")
        logger.info(
            f"test_get_session_history_invalid_uuid API call executed in {time.time() - start_time:.4f} seconds"
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["code"] == "HST0003"
        assert response_data["data"] == {"error": "Invalid session ID format"}
        assert response_data["message"] == "Invalid UUID"
