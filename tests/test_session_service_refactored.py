"""Tests for the refactored SessionService components."""

import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch
from typing import List

import pytest

# Mock the external dependencies
@pytest.fixture
def mock_db_session():
    """Mock database session."""
    session = AsyncMock()
    return session

@pytest.fixture
def mock_user_id():
    """Mock user ID."""
    return uuid.uuid4()

@pytest.fixture
def mock_session_id():
    """Mock session ID."""
    return uuid.uuid4()

@pytest.fixture
def mock_chat_history():
    """Mock chat history."""
    from app.src.schemas.chat_sessions import ChatHistoryItem
    return [
        ChatHistoryItem(role="user", content="Hello", timestamp=datetime.now()),
        ChatHistoryItem(role="assistant", content="Hi there!", timestamp=datetime.now()),
        ChatHistoryItem(role="user", content="How are you?", timestamp=datetime.now()),
        ChatHistoryItem(role="assistant", content="I'm doing well!", timestamp=datetime.now()),
    ]

class TestSessionServiceRefactored:
    """Test the refactored SessionService."""

    @patch('app.src.services.session.core.session_manager.ChatSessionRepository')
    @patch('app.src.services.session.core.conversation_manager.ChatConversationRepository')
    @patch('app.src.services.session.managers.cleanup_manager.ChatSessionRepository')
    def test_session_service_initialization(self, mock_cleanup_repo, mock_conv_repo, mock_session_repo):
        """Test that SessionService initializes all components correctly."""
        from app.src.services.session_service import SessionService
        
        service = SessionService()
        
        # Verify all managers are initialized
        assert hasattr(service, 'session_manager')
        assert hasattr(service, 'conversation_manager')
        assert hasattr(service, 'summary_manager')
        assert hasattr(service, 'token_manager')
        assert hasattr(service, 'cleanup_manager')

    def test_token_manager_count_tokens(self):
        """Test TokenManager token counting functionality."""
        from app.src.services.session.managers.token_manager import TokenManager
        
        # Test basic token counting (mocked)
        with patch('tiktoken.encoding_for_model') as mock_encoding:
            mock_enc = MagicMock()
            mock_enc.encode.return_value = [1, 2, 3, 4, 5]  # 5 tokens
            mock_encoding.return_value = mock_enc
            
            count = TokenManager.count_tokens("Hello world")
            assert count == 5

    def test_token_manager_should_truncate_history(self, mock_chat_history):
        """Test TokenManager history truncation logic."""
        from app.src.services.session.managers.token_manager import TokenManager
        
        with patch.object(TokenManager, 'count_tokens', return_value=5000):  # High token count
            with patch.object(TokenManager, 'get_max_token_limit', return_value=4000):
                should_truncate = TokenManager.should_truncate_history(mock_chat_history, "New question")
                assert should_truncate is True

        with patch.object(TokenManager, 'count_tokens', return_value=2000):  # Low token count
            with patch.object(TokenManager, 'get_max_token_limit', return_value=4000):
                should_truncate = TokenManager.should_truncate_history(mock_chat_history, "New question")
                assert should_truncate is False

    def test_token_manager_get_messages_to_summarize(self, mock_chat_history):
        """Test TokenManager message selection for summarization."""
        from app.src.services.session.managers.token_manager import TokenManager
        
        # Test with more messages than keep_recent
        messages_to_summarize = TokenManager.get_messages_to_summarize(mock_chat_history, keep_recent=2)
        assert len(messages_to_summarize) == 2  # Should return first 2 messages
        
        # Test with fewer messages than keep_recent
        messages_to_summarize = TokenManager.get_messages_to_summarize(mock_chat_history, keep_recent=10)
        assert len(messages_to_summarize) == 4  # Should return all messages

    @patch('app.src.services.session.core.session_manager.ChatSessionRepository')
    async def test_session_manager_create_session(self, mock_repo, mock_db_session, mock_user_id):
        """Test SessionManager session creation."""
        from app.src.services.session.core.session_manager import SessionManager
        
        # Mock repository response
        mock_session = MagicMock()
        mock_session.session_id = uuid.uuid4()
        mock_repo.return_value.create.return_value = mock_session
        
        session_manager = SessionManager()
        session_id = await session_manager.create_session(mock_db_session, mock_user_id)
        
        assert session_id == mock_session.session_id
        mock_repo.return_value.create.assert_called_once()

    @patch('app.src.services.session.core.conversation_manager.ChatConversationRepository')
    async def test_conversation_manager_add_conversation(self, mock_repo, mock_db_session, mock_session_id):
        """Test ConversationManager conversation addition."""
        from app.src.services.session.core.conversation_manager import ConversationManager
        
        mock_repo.return_value.create.return_value = MagicMock()
        
        conversation_manager = ConversationManager()
        result = await conversation_manager.add_conversation(
            mock_db_session, mock_session_id, "Hello", "Hi there!"
        )
        
        assert result is True
        # Should be called twice (user + assistant)
        assert mock_repo.return_value.create.call_count == 2

    def test_conversation_manager_format_conversation_text(self, mock_chat_history):
        """Test ConversationManager text formatting."""
        from app.src.services.session.core.conversation_manager import ConversationManager
        
        conversation_manager = ConversationManager()
        formatted_text = conversation_manager.format_conversation_text(mock_chat_history)
        
        expected_lines = [
            "user: Hello",
            "assistant: Hi there!",
            "user: How are you?",
            "assistant: I'm doing well!"
        ]
        assert formatted_text == "\n".join(expected_lines)

    @patch('app.src.services.session.managers.cleanup_manager.ChatSessionRepository')
    async def test_cleanup_manager_clean_old_sessions(self, mock_repo, mock_db_session):
        """Test CleanupManager session cleanup."""
        from app.src.services.session.managers.cleanup_manager import CleanupManager
        
        mock_repo.return_value.clean_old_sessions.return_value = 5  # 5 sessions cleaned
        
        cleanup_manager = CleanupManager()
        count = await cleanup_manager.clean_old_sessions(mock_db_session, days_threshold=30)
        
        assert count == 5
        mock_repo.return_value.clean_old_sessions.assert_called_once_with(mock_db_session, 30)

    @patch('app.src.services.session.managers.summary_manager.SessionManager')
    @patch('app.src.services.session.managers.summary_manager.ConversationManager')
    async def test_summary_manager_generate_session_summary(
        self, mock_conv_manager, mock_session_manager, mock_db_session, mock_session_id, mock_user_id
    ):
        """Test SummaryManager session summary generation."""
        from app.src.services.session.managers.summary_manager import SummaryManager
        
        # Mock LLM service
        mock_llm_service = MagicMock()
        mock_response = MagicMock()
        mock_response.choices[0].message.content = "Test Summary"
        mock_llm_service.openai_client.chat.completions.create.return_value = mock_response
        
        # Mock request object
        mock_request = MagicMock()
        mock_request.model = "gpt-4"
        mock_request.max_tokens = 100
        mock_request.temperature = 0.7
        
        summary_manager = SummaryManager()
        summary = await summary_manager.generate_session_summary(
            mock_llm_service, mock_db_session, mock_session_id, mock_user_id, 
            mock_request, ["Hello", "Hi there!"]
        )
        
        assert summary == "Test Summary"
        mock_llm_service.openai_client.chat.completions.create.assert_called_once()

    @patch('app.src.services.session_service.SessionManager')
    @patch('app.src.services.session_service.ConversationManager')
    @patch('app.src.services.session_service.SummaryManager')
    @patch('app.src.services.session_service.TokenManager')
    @patch('app.src.services.session_service.CleanupManager')
    async def test_session_service_integration(
        self, mock_cleanup, mock_token, mock_summary, mock_conv, mock_session,
        mock_db_session, mock_user_id, mock_session_id
    ):
        """Test SessionService integration with all components."""
        from app.src.services.session_service import SessionService
        
        # Mock component responses
        mock_session.return_value.create_session.return_value = mock_session_id
        mock_conv.return_value.add_conversation.return_value = True
        mock_conv.return_value.get_conversation_history.return_value = []
        mock_summary.return_value.generate_session_summary.return_value = "Summary"
        mock_token.should_truncate_history.return_value = False
        mock_cleanup.return_value.clean_old_sessions.return_value = 3
        
        service = SessionService()
        
        # Test session creation
        session_id = await service.create_session(mock_db_session, mock_user_id)
        assert session_id == mock_session_id
        
        # Test conversation addition
        result = await service.add_conversation(mock_db_session, mock_session_id, "Q", "A")
        assert result is True
        
        # Test history retrieval
        history = await service.get_conversation_history(mock_db_session, mock_session_id)
        assert history == []
        
        # Test cleanup
        count = await service.clean_old_sessions(mock_db_session)
        assert count == 3

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
