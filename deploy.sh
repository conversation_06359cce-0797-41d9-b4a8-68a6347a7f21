#! /usr/bin/env sh
aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin 626635442929.dkr.ecr.ap-northeast-1.amazonaws.com
docker build -f cicd/Dockerfile --build-arg PACKAGE_REGISTRY_TOKEN=-vhNjRftLU4s-mhYrC4F -t dev-repository-ourads .

docker tag dev-repository-ourads:latest 626635442929.dkr.ecr.ap-northeast-1.amazonaws.com/dev-repository-ourads:latest

docker push 626635442929.dkr.ecr.ap-northeast-1.amazonaws.com/dev-repository-ourads:latest

