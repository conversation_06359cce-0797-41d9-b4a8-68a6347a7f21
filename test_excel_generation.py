#!/usr/bin/env python3
"""
Test script to verify Excel generation for Google platforms.
"""

import asyncio
import json
import tempfile
import os
from pathlib import Path

# Add the app directory to the path
import sys
sys.path.append('/home/<USER>/Documents/Project/our-ad/our-ad-ai-be')

from app.src.services.llm.excel.formatters.google_search_formatter import GoogleSearchFormatter
from app.src.services.llm.excel.formatters.google_display_formatter import GoogleDisplayFormatter
from app.src.services.llm.excel.formatters.google_demand_gen_formatter import GoogleDemandGenFormatter

# Sample campaign data for testing
SAMPLE_CAMPAIGN_DATA = {
    "data": {
        "媒体": "Google",
        "キャンペーン": "テストキャンペーン",
        "広告グループ": ["グループ1", "グループ2"],
        "メインキーワード": ["キーワード1", "キーワード2"],
        "掛合わせ1": ["掛合わせ1", "掛合わせ2"],
        "マッチタイプ": ["完全一致", "部分一致"],
        "広告": ["広告1", "広告2"],
        "見出し": ["見出し1", "見出し2", "見出し3"],
        "説明文": ["説明文1", "説明文2"],
        "パス": ["パス1", "パス2"],
        "入稿先URL": "https://example.com",
        "地域": "日本全国",
        "性別": "すべて",
        "年齢": "18-65",
        "年収": "すべて",
        "ターゲティング": "興味関心",
        "備考": "テスト用",
        "キャンペーン名": "テストキャンペーン",
        "広告グループ名": ["グループ1", "グループ2"],
        "配信条件": ["条件1", "条件2"],
        "デバイス": "すべて",
        "エリア": "日本全国",
        "Google　URL": "https://example.com?utm_source=google&utm_medium=display",
        "広告種類": "レスポンシブ ディスプレイ広告",
        "主体者表記": "株式会社テスト",
        "広告見出し 短縮": ["短縮見出し1", "短縮見出し2"],
        "広告文": {
            "Google": {
                "主体者表記": "株式会社テスト",
                "広告見出し": "メイン見出し",
                "広告見出し 短縮": ["短縮1", "短縮2"],
                "説明文": ["説明1", "説明2"],
                "URL": "https://example.com?utm_source=google&utm_medium=display"
            }
        },
        "除外プレースメント": "除外サイト"
    },
    "missing_fields": []
}

SAMPLE_OUTPUT_FORMAT = {
    "fields": {
        "キャンペーン": {"type": "string", "required": True},
        "広告グループ": {"type": "array", "max_count": 2},
        "見出し": {"type": "array", "max_count": 15, "max_chars": 30}
    }
}

async def test_google_search_formatter():
    """Test Google Search Ads formatter."""
    print("Testing Google Search Ads formatter...")
    formatter = GoogleSearchFormatter()
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        try:
            await formatter.write_excel_file(
                campaign_data=SAMPLE_CAMPAIGN_DATA,
                output_format=SAMPLE_OUTPUT_FORMAT,
                file_path=tmp_file.name
            )
            print(f"✅ Google Search Ads Excel file created: {tmp_file.name}")
            print(f"   File size: {os.path.getsize(tmp_file.name)} bytes")
            return True
        except Exception as e:
            print(f"❌ Google Search Ads formatter failed: {e}")
            return False
        finally:
            # Clean up
            if os.path.exists(tmp_file.name):
                os.unlink(tmp_file.name)

async def test_google_display_formatter():
    """Test Google Display Ads formatter."""
    print("Testing Google Display Ads formatter...")
    formatter = GoogleDisplayFormatter()
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        try:
            await formatter.write_excel_file(
                campaign_data=SAMPLE_CAMPAIGN_DATA,
                output_format=SAMPLE_OUTPUT_FORMAT,
                file_path=tmp_file.name
            )
            print(f"✅ Google Display Ads Excel file created: {tmp_file.name}")
            print(f"   File size: {os.path.getsize(tmp_file.name)} bytes")
            return True
        except Exception as e:
            print(f"❌ Google Display Ads formatter failed: {e}")
            return False
        finally:
            # Clean up
            if os.path.exists(tmp_file.name):
                os.unlink(tmp_file.name)

async def test_google_demand_gen_formatter():
    """Test Google Demand Gen Ads formatter."""
    print("Testing Google Demand Gen Ads formatter...")
    formatter = GoogleDemandGenFormatter()
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        try:
            await formatter.write_excel_file(
                campaign_data=SAMPLE_CAMPAIGN_DATA,
                output_format=SAMPLE_OUTPUT_FORMAT,
                file_path=tmp_file.name
            )
            print(f"✅ Google Demand Gen Ads Excel file created: {tmp_file.name}")
            print(f"   File size: {os.path.getsize(tmp_file.name)} bytes")
            return True
        except Exception as e:
            print(f"❌ Google Demand Gen Ads formatter failed: {e}")
            return False
        finally:
            # Clean up
            if os.path.exists(tmp_file.name):
                os.unlink(tmp_file.name)

async def main():
    """Run all tests."""
    print("🧪 Testing Google Platform Excel Formatters")
    print("=" * 50)
    
    results = []
    
    # Test all formatters
    results.append(await test_google_search_formatter())
    results.append(await test_google_display_formatter())
    results.append(await test_google_demand_gen_formatter())
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Passed: {sum(results)}/{len(results)}")
    print(f"   Failed: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 All tests passed!")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
