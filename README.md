# Chatbot-Ads

Chatbot-Ads is an AI-powered tool designed to generate tailored advertisements for multiple platforms, including Facebook, Instagram, Twitter, LinkedIn, and YouTube. By leveraging advanced language models and platform-specific prompts, it creates optimized ad content to enhance campaign performance.

## Installation
1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-repo/chatbot-ads.git
   cd chatbot-ads

2. **Set up a virtual environment:**:
   ```bash
   python -m venv venv
   source venv/bin/activate

3. **Install dependencies:**:
   ```bash
   pip install -r cicd/requirements/requirements.txt
   pip install -r cicd/requirements/requirements-style.txt
   ```
## Usage
1. **Check lint and format code**
   ```bash
   ruff format 
   ruff check --fix
   ```
2. **Check security code**
   ```bash
   bandit --exclude venv --recursive .

   dodgy --ignore-paths venv/

   safety scan -r cicd/requirements/requirements.txt
   ```
3. **Check unitest**
   ```
   pytest tests
   ```
4. **Run the application**
   ```bash
   python app/main.py
   ```

