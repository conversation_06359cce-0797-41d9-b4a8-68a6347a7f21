#!/usr/bin/env python3
"""
Test script to verify the refactored LLM service structure.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, '/home/<USER>/Documents/Project/our-ad/our-ad-ai-be')

def test_imports():
    """Test that all modules can be imported."""
    try:
        # Test if files exist and have correct structure
        import os

        base_path = "/home/<USER>/Documents/Project/our-ad/our-ad-ai-be/app/src/services/llm"

        # Check core modules
        assert os.path.exists(f"{base_path}/core/llm_client.py"), "LLMClient file missing"
        print("✓ LLMClient file exists")

        assert os.path.exists(f"{base_path}/core/stream_processor.py"), "StreamProcessor file missing"
        print("✓ StreamProcessor file exists")

        # Check tool modules
        assert os.path.exists(f"{base_path}/tools/base_tool.py"), "BaseTool file missing"
        print("✓ BaseTool file exists")

        assert os.path.exists(f"{base_path}/tools/web_search_tool.py"), "WebSearchTool file missing"
        print("✓ WebSearchTool file exists")

        assert os.path.exists(f"{base_path}/tools/excel_tool.py"), "ExcelTool file missing"
        print("✓ ExcelTool file exists")

        assert os.path.exists(f"{base_path}/tools/csv_tool.py"), "CSVTool file missing"
        print("✓ CSVTool file exists")

        # Check Excel modules
        assert os.path.exists(f"{base_path}/excel/excel_manager.py"), "ExcelManager file missing"
        print("✓ ExcelManager file exists")

        # Check main service
        assert os.path.exists(f"{base_path}/llm_service.py"), "RefactoredLLMService file missing"
        print("✓ RefactoredLLMService file exists")

        # Check wrapper service
        wrapper_path = "/home/<USER>/Documents/Project/our-ad/our-ad-ai-be/app/src/services/llm_service.py"
        assert os.path.exists(wrapper_path), "Wrapper LLMService file missing"
        print("✓ Wrapper LLMService file exists")

        print("\n🎉 All files exist! Refactoring structure is correct.")
        return True

    except AssertionError as e:
        print(f"❌ File missing: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_structure():
    """Test the basic structure of the refactored service."""
    try:
        # Check that the files have the expected class definitions
        import ast

        # Check main service file
        service_path = "/home/<USER>/Documents/Project/our-ad/our-ad-ai-be/app/src/services/llm/llm_service.py"
        with open(service_path, 'r') as f:
            content = f.read()
            tree = ast.parse(content)

        # Find LLMService class
        llm_service_found = False
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == 'LLMService':
                llm_service_found = True
                break

        assert llm_service_found, "LLMService class not found in refactored service"
        print("✓ LLMService class found in refactored service")

        # Check wrapper service file
        wrapper_path = "/home/<USER>/Documents/Project/our-ad/our-ad-ai-be/app/src/services/llm_service.py"
        with open(wrapper_path, 'r') as f:
            content = f.read()
            tree = ast.parse(content)

        # Find wrapper LLMService class
        wrapper_service_found = False
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == 'LLMService':
                wrapper_service_found = True
                break

        assert wrapper_service_found, "LLMService class not found in wrapper service"
        print("✓ LLMService class found in wrapper service")

        print("✓ Service structure is correct")
        return True

    except Exception as e:
        print(f"❌ Structure test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing refactored LLM service structure...\n")
    
    import_success = test_imports()
    structure_success = test_structure()
    
    if import_success and structure_success:
        print("\n🎉 All tests passed! Refactoring is successful.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed.")
        sys.exit(1)
