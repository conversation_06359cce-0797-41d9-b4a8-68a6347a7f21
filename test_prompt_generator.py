#!/usr/bin/env python3
"""
Test script to verify PromptGener<PERSON> works with ad extensions format.
"""

import sys
import json

# Add the app directory to the path
sys.path.append('/home/<USER>/Documents/Project/our-ad/our-ad-ai-be')

def test_prompt_generator():
    """Test PromptGenerator with ad extensions format."""
    print("🧪 Testing PromptGenerator with Ad Extensions Format")
    print("=" * 60)
    
    try:
        from app.src.services.llm.utils.prompt_generator import PromptGenerator
        
        # Load the ad extensions format
        with open('/home/<USER>/Documents/Project/our-ad/our-ad-ai-be/app/src/data/outputs/ad_extensions_format.json', 'r', encoding='utf-8') as f:
            ad_extensions_format = json.load(f)
        
        print("✅ Ad extensions format loaded successfully")
        
        # Create prompt generator
        prompt_generator = PromptGenerator()
        print("✅ PromptGenerator created successfully")
        
        # Extract fields
        fields = ad_extensions_format.get("fields", {})
        print(f"✅ Found {len(fields)} fields in format")
        
        # Generate prompt
        platform = "Ad Extensions"
        prompt = prompt_generator.generate_prompt_from_output_format(
            platform=platform,
            fields=fields
        )
        
        print("✅ Prompt generated successfully!")
        print("\n" + "=" * 60)
        print("Generated Prompt:")
        print("=" * 60)
        print(prompt)
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comparison_with_other_format():
    """Test with a regular format for comparison."""
    print("\n🔍 Testing with Google Search format for comparison")
    print("=" * 60)
    
    try:
        from app.src.services.llm.utils.prompt_generator import PromptGenerator
        
        # Load the Google Search format
        with open('/home/<USER>/Documents/Project/our-ad/our-ad-ai-be/app/src/data/outputs/google_search_ads_format.json', 'r', encoding='utf-8') as f:
            google_format = json.load(f)
        
        print("✅ Google Search format loaded successfully")
        
        # Create prompt generator
        prompt_generator = PromptGenerator()
        
        # Extract fields
        fields = google_format.get("fields", {})
        print(f"✅ Found {len(fields)} fields in format")
        
        # Generate prompt
        platform = "Google Search Ads"
        prompt = prompt_generator.generate_prompt_from_output_format(
            platform=platform,
            fields=fields
        )
        
        print("✅ Google Search prompt generated successfully!")
        print("\n" + "=" * 60)
        print("Google Search Generated Prompt (first 500 chars):")
        print("=" * 60)
        print(prompt[:500] + "..." if len(prompt) > 500 else prompt)
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Google Search test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_prompt_generator()
    success2 = test_comparison_with_other_format()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("💥 Some tests failed!")
        sys.exit(1)
