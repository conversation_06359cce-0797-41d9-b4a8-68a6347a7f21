#!/usr/bin/env python3
"""
Test script for Excel error handling improvements.

This script tests various error scenarios to ensure proper error messages
are returned to users when Excel file creation fails.
"""

import asyncio
import json
import os
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

async def test_content_validation():
    """Test advertising content validation logic."""
    print("🧪 Testing content validation...")
    
    try:
        from app.src.services.llm.tools.excel_tool import ExcelTool
        from app.src.services.prompt_service import PromptService
        
        # Mock dependencies
        prompt_service = Mock(spec=PromptService)
        llm_client = Mock()
        
        excel_tool = ExcelTool(prompt_service, llm_client)
        
        # Test 1: Empty messages
        print("  Test 1: Empty conversation history")
        result = excel_tool._has_advertising_content([])
        assert not result, "Should return False for empty messages"
        print("  ✅ Empty messages correctly identified as no content")
        
        # Test 2: Messages without advertising content
        print("  Test 2: Messages without advertising content")
        messages_no_content = [
            {"role": "user", "content": "こんにちは"},
            {"role": "assistant", "content": "こんにちは！何かお手伝いできることはありますか？"}
        ]
        result = excel_tool._has_advertising_content(messages_no_content)
        assert not result, "Should return False for messages without ad content"
        print("  ✅ Non-advertising messages correctly identified")
        
        # Test 3: Messages with advertising content
        print("  Test 3: Messages with advertising content")
        messages_with_content = [
            {"role": "user", "content": "新商品の広告を作成してください"},
            {"role": "assistant", "content": "新商品の広告を作成いたします。以下のような見出しと説明文をご提案します：\n\n見出し：「革新的な新商品登場」\n説明文：「この商品は素晴らしい機能を持っています」\nキャンペーン名：「新商品プロモーション」\nターゲット：20-40代の女性"}
        ]
        result = excel_tool._has_advertising_content(messages_with_content)
        assert result, "Should return True for messages with ad content"
        print("  ✅ Advertising content correctly identified")
        
        # Test 4: Sufficient content validation
        print("  Test 4: Sufficient content validation")
        sufficient_data = {
            "data": {
                "見出し": ["素晴らしい商品", "今すぐ購入"],
                "説明文": ["詳細な商品説明", "魅力的な特徴"],
                "キャンペーン名": "春のセール"
            }
        }
        result = excel_tool._has_sufficient_content(sufficient_data)
        assert result, "Should return True for sufficient content"
        print("  ✅ Sufficient content correctly validated")
        
        # Test 5: Insufficient content validation
        print("  Test 5: Insufficient content validation")
        insufficient_data = {
            "data": {
                "見出し": [""],
                "説明文": [],
                "キャンペーン名": ""
            }
        }
        result = excel_tool._has_sufficient_content(insufficient_data)
        assert not result, "Should return False for insufficient content"
        print("  ✅ Insufficient content correctly identified")
        
        print("✅ All content validation tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Content validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_excel_creation_errors():
    """Test Excel creation error handling."""
    print("\n🧪 Testing Excel creation error handling...")
    
    try:
        from app.src.services.llm.tools.excel_tool import ExcelTool
        from app.src.services.prompt_service import PromptService
        
        # Mock dependencies
        prompt_service = Mock(spec=PromptService)
        llm_client = Mock()
        
        excel_tool = ExcelTool(prompt_service, llm_client)
        
        # Test 1: Unsupported platform
        print("  Test 1: Unsupported platform error")
        try:
            await excel_tool.execute("UnsupportedPlatform")
            assert False, "Should have raised an exception"
        except Exception as e:
            assert "対応していません" in str(e), f"Expected platform error message, got: {str(e)}"
            print("  ✅ Unsupported platform error handled correctly")
        
        # Test 2: No advertising content
        print("  Test 2: No advertising content error")
        try:
            conversation_context = {
                "messages": [
                    {"role": "user", "content": "こんにちは"},
                    {"role": "assistant", "content": "こんにちは！"}
                ]
            }
            await excel_tool.execute("Meta (Instagram/Facebook)", conversation_context=conversation_context)
            assert False, "Should have raised an exception"
        except Exception as e:
            assert "広告コンテンツが見つかりません" in str(e), f"Expected content error message, got: {str(e)}"
            print("  ✅ No advertising content error handled correctly")
        
        print("✅ Excel creation error tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Excel creation error test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_file_system_errors():
    """Test file system error handling."""
    print("\n🧪 Testing file system error handling...")
    
    try:
        from app.src.services.llm.excel.excel_manager import ExcelManager
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            outputs_dir = Path(temp_dir)
            excel_manager = ExcelManager(outputs_dir)
            
            # Test 1: Missing format file
            print("  Test 1: Missing format file error")
            try:
                await excel_manager.create_excel_file("Meta (Instagram/Facebook)", {"data": {}})
                assert False, "Should have raised an exception"
            except Exception as e:
                assert "format file" in str(e).lower() or "見つかりません" in str(e), f"Expected format file error, got: {str(e)}"
                print("  ✅ Missing format file error handled correctly")
            
            # Test 2: Invalid JSON format file
            print("  Test 2: Invalid JSON format file error")
            invalid_json_file = outputs_dir / "meta_ads_format.json"
            invalid_json_file.write_text("{ invalid json }", encoding='utf-8')
            
            try:
                await excel_manager.create_excel_file("Meta (Instagram/Facebook)", {"data": {}})
                assert False, "Should have raised an exception"
            except Exception as e:
                assert "json" in str(e).lower() or "解析" in str(e), f"Expected JSON error, got: {str(e)}"
                print("  ✅ Invalid JSON error handled correctly")
        
        print("✅ File system error tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ File system error test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_error_message_quality():
    """Test that error messages are user-friendly and actionable."""
    print("\n🧪 Testing error message quality...")
    
    try:
        from app.src.services.llm.tools.excel_tool import ExcelTool
        from app.src.services.prompt_service import PromptService
        
        # Mock dependencies
        prompt_service = Mock(spec=PromptService)
        llm_client = Mock()
        
        excel_tool = ExcelTool(prompt_service, llm_client)
        
        # Test error message for no content
        try:
            conversation_context = {"messages": []}
            await excel_tool.execute("Meta (Instagram/Facebook)", conversation_context=conversation_context)
        except Exception as e:
            error_msg = str(e)
            print(f"  Error message: {error_msg}")
            
            # Check that the message is user-friendly
            assert "❌" in error_msg, "Error message should start with error emoji"
            assert "広告コンテンツ" in error_msg, "Error message should mention advertising content"
            assert "作成してください" in error_msg, "Error message should provide actionable guidance"
            assert "再作成" in error_msg, "Error message should mention recreation option"
            
            print("  ✅ Error message is user-friendly and actionable")
        
        print("✅ Error message quality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error message quality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all error handling tests."""
    print("🚀 Starting Excel error handling tests...\n")
    
    tests = [
        test_content_validation,
        test_excel_creation_errors,
        test_file_system_errors,
        test_error_message_quality
    ]
    
    results = []
    for test in tests:
        result = await test()
        results.append(result)
    
    print(f"\n📊 Test Results:")
    print(f"  Passed: {sum(results)}/{len(results)}")
    print(f"  Failed: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("\n🎉 All tests passed! Excel error handling improvements are working correctly.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        return False

if __name__ == "__main__":
    asyncio.run(main())
