#!/usr/bin/env python3
"""
Simple test for Ad Extensions Excel generation.
"""

import sys
import tempfile
import os

# Add the app directory to the path
sys.path.append('/home/<USER>/Documents/Project/our-ad/our-ad-ai-be')

def main():
    print("🧪 Simple Ad Extensions Test")
    print("=" * 40)
    
    try:
        # Import the formatter
        from app.src.services.llm.excel.formatters.ad_extensions_formatter import AdExtensionsFormatter
        print("✅ Import successful")
        
        # Create formatter instance
        formatter = AdExtensionsFormatter()
        print("✅ Formatter created")
        
        # Test data
        campaign_data = {
            "data": {
                "platform_selected": "Google",
                "サイトリンク": [
                    {
                        "見出し": "テストリンク",
                        "説明文1": "説明文1",
                        "説明文2": "説明文2",
                        "URL": "https://example.com"
                    }
                ],
                "コールアウト": ["テストコールアウト"],
                "電話番号表示": "03-1234-5678"
            }
        }
        
        output_format = {"fields": {}}
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            print(f"Creating file: {tmp_file.name}")
            
            # This is the async call - let's see if this is where it hangs
            print("About to call write_excel_file...")
            
            # Try to call the method synchronously first to see if it's the async that's the issue
            try:
                # Let's check if the method signature is correct
                import inspect
                sig = inspect.signature(formatter.write_excel_file)
                print(f"Method signature: {sig}")
                
                # Try calling it (this will fail if it's truly async)
                result = formatter.write_excel_file(campaign_data, output_format, tmp_file.name)
                print(f"Result type: {type(result)}")
                
                if hasattr(result, '__await__'):
                    print("Method is async - need to use asyncio")
                    import asyncio
                    asyncio.run(result)
                else:
                    print("Method is sync")
                
                print("✅ Excel file creation completed")
                
                if os.path.exists(tmp_file.name):
                    size = os.path.getsize(tmp_file.name)
                    print(f"✅ File created successfully! Size: {size} bytes")
                else:
                    print("❌ File was not created")
                    
            except Exception as e:
                print(f"❌ Excel creation failed: {e}")
                import traceback
                traceback.print_exc()
            finally:
                # Clean up
                if os.path.exists(tmp_file.name):
                    os.unlink(tmp_file.name)
                    print("🧹 Cleaned up temporary file")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("=" * 40)
    print("Test complete")
    return 0

if __name__ == "__main__":
    sys.exit(main())
