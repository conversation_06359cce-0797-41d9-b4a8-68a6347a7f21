#========================
# Define pipeline stages
#========================
stages:
  - lint
  - test
  - scan
  - build

image: python:3.10

cache:
  paths:
    - .cache/pip

.before_script_build: &before_script_build
  before_script:
    - rm -rf build dist *.egg-info
    - apt-get update && apt-get install -y twine

#========================
# Define jobs
#========================
lint-code:
  stage: lint
  needs: [ ]
  before_script:
    - python -m pip install --upgrade pip
    - pip install typing_extensions isort black flake8
  script:
    - bash scripts/lint.sh
  allow_failure: true

unit-test:
  stage: test
  needs: [ ]
  before_script:
    - python -m pip install --upgrade pip
    - pip install pytest onnxruntime flysystem requests
    - if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    - if [ -f requirements-dev.txt ]; then pip install -r requirements-dev.txt; fi
  script:
    - bash scripts/test.sh
  allow_failure: true

sast:
  stage: scan
  needs: [ ]

dependency_scanning:
  stage: scan
  needs: [ ]

secret_detection:
  stage: scan
  needs: [ ]

latest-image:
  stage: build
  <<: *before_script_build
  script:
    - python -m pip install --upgrade pip
    - pip install cython setuptools wheel
    - python setup.py sdist bdist_wheel
    - TWINE_PASSWORD=${CI_JOB_TOKEN} TWINE_USERNAME=gitlab-ci-token twine upload --verbose --repository-url https://git.rabiloo.net/api/v4/projects/${CI_PROJECT_ID}/packages/pypi dist/*
  only:
    - master
    - main

include:
  - template: Security/SAST.gitlab-ci.yml
  - template: Security/Secret-Detection.gitlab-ci.yml
  - template: Security/Dependency-Scanning.gitlab-ci.yml
