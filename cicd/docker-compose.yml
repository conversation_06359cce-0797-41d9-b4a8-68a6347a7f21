services:
  coordinator:
    image: citusdata/citus:latest
    container_name: our-ads-test
    hostname: our-ad-db
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_NAME}
    ports:
      - ${DB_PORT}:5432
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -d ${DB_NAME} -U ${DB_USER} -h localhost -p 5432"
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - /data/coordinator-init:/docker-entrypoint-initdb.d
    networks:
      - our-ai-net

  our-ai-be:
    image: our-ai-be:latest
    container_name: our-ai-be-test
    hostname: our-ai-be
    restart: on-failure
    env_file:
      - .env
    ports:
      - ${PORT:-30103}:30103
    depends_on:
      coordinator:
        condition: service_healthy
    healthcheck:
      test: "curl --fail http://localhost:${PORT:-30103}/actuator/health || exit 1"
      interval: 30s
      timeout: 5s
      start_period: 20s
      retries: 3
    volumes:
      - app_logs:/home/<USER>/logs
    networks:
      - our-ai-net

volumes:
  app_logs:

networks:
  our-ai-net:
    driver: bridge
