FROM python:3.10
ARG PYTHON_VERSION=3.10

ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        python3-pip \
        ca-certificates \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /tmp

ARG PACKAGE_REGISTRY_TOKEN
ENV PACKAGE_REGISTRY_TOKEN=$PACKAGE_REGISTRY_TOKEN

# Copy requirements
COPY cicd/requirements/requirements.txt ./

# Install any needed packages specified in requirements.txt
RUN pip3 install --no-cache-dir -r requirements.txt

ARG USERNAME=rbl
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# Create the user
RUN groupadd --gid $USER_GID $USERNAME \
    && useradd --uid $USER_UID --gid $USER_GID $USERNAME
# Change to non-root privilege
USER $USERNAME

WORKDIR /home/<USER>/app

# Copy app source code
COPY --chown=$USER_UID:$USER_GID ../app/ ./
COPY --chown=$USER_UID:$USER_GID migration/ ./
COPY --chown=$USER_UID:$USER_GID start.sh ./
COPY .env ./

ENV PYTHONPATH=/home/<USER>
ENTRYPOINT ["./start.sh"]
