#!/usr/bin/env python3
"""
Test script to verify the Excel tool execution logic.
"""

import sys
import os
import asyncio
from unittest.mock import Mock, AsyncMock

# Add the project root to the Python path
sys.path.insert(0, '/home/<USER>/Documents/Project/our-ad/our-ad-ai-be')

async def test_excel_tool_execution():
    """Test the Excel tool execution with conversation context."""
    try:
        # Mock dependencies
        mock_excel_manager = Mock()
        mock_excel_manager.platform_mapping = {
            "Meta (Instagram/Facebook)": "meta_ads_format.json"
        }
        mock_excel_manager._load_output_format = AsyncMock(return_value={
            "fields": {
                "キャンペーン名": {"type": "string", "required": True},
                "広告グループ名": {"type": "array", "required": True}
            }
        })
        mock_excel_manager.create_excel_file = AsyncMock(return_value="/download/test.xlsx")
        
        mock_prompt_generator = Mock()
        mock_prompt_generator.generate_prompt_from_output_format = Mock(
            return_value="Generate campaign data for Meta platform"
        )
        
        mock_prompt_service = Mock()
        mock_prompt_service._get_prompts = AsyncMock(return_value="System prompt")
        
        mock_llm_client = Mock()
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '{"data": {"キャンペーン名": "テストキャンペーン"}, "missing_fields": []}'
        mock_llm_client.create_chat_completion = AsyncMock(return_value=mock_response)
        
        # Import and create Excel tool
        from app.src.services.llm.tools.excel_tool import ExcelTool
        
        excel_tool = ExcelTool(
            excel_manager=mock_excel_manager,
            prompt_generator=mock_prompt_generator,
            prompt_service=mock_prompt_service,
            llm_client=mock_llm_client
        )
        
        # Test conversation context
        conversation_context = {
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Create a Meta campaign for shoes"},
                {"role": "assistant", "content": "I'll create a Meta campaign for shoes"}
            ],
            "tool_call_id": "test_call_123"
        }
        
        # Execute the tool
        result = await excel_tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context=conversation_context
        )
        
        # Verify result
        assert "download_url" in result, "Result should contain download_url"
        assert result["download_url"] == "/download/test.xlsx", "Download URL should match"
        
        # Verify mocks were called
        mock_excel_manager._load_output_format.assert_called_once()
        mock_prompt_generator.generate_prompt_from_output_format.assert_called_once()
        mock_prompt_service._get_prompts.assert_called_once()
        mock_llm_client.create_chat_completion.assert_called_once()
        mock_excel_manager.create_excel_file.assert_called_once()
        
        print("✓ Excel tool execution test passed")
        return True
        
    except Exception as e:
        print(f"❌ Excel tool execution test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_excel_tool_fallback():
    """Test the Excel tool fallback when LLM extraction fails."""
    try:
        # Mock dependencies with failing LLM
        mock_excel_manager = Mock()
        mock_excel_manager.platform_mapping = {
            "Meta (Instagram/Facebook)": "meta_ads_format.json"
        }
        mock_excel_manager._load_output_format = AsyncMock(return_value={
            "fields": {"キャンペーン名": {"type": "string"}}
        })
        mock_excel_manager.create_excel_file = AsyncMock(return_value="/download/fallback.xlsx")
        
        mock_prompt_generator = Mock()
        mock_prompt_generator.generate_prompt_from_output_format = Mock(return_value="Generate data")
        
        mock_prompt_service = Mock()
        mock_prompt_service._get_prompts = AsyncMock(return_value="System prompt")
        
        # Mock LLM client that fails
        mock_llm_client = Mock()
        mock_llm_client.create_chat_completion = AsyncMock(side_effect=Exception("LLM failed"))
        
        # Import and create Excel tool
        from app.src.services.llm.tools.excel_tool import ExcelTool
        
        excel_tool = ExcelTool(
            excel_manager=mock_excel_manager,
            prompt_generator=mock_prompt_generator,
            prompt_service=mock_prompt_service,
            llm_client=mock_llm_client
        )
        
        # Execute the tool (should fallback to default data)
        result = await excel_tool.execute(
            platform="Meta (Instagram/Facebook)",
            conversation_context={"messages": []}
        )
        
        # Verify result
        assert "download_url" in result, "Result should contain download_url"
        assert result["download_url"] == "/download/fallback.xlsx", "Download URL should match"
        
        # Verify fallback was used
        mock_excel_manager.create_excel_file.assert_called_once()
        call_args = mock_excel_manager.create_excel_file.call_args[0]
        campaign_data = call_args[1]
        assert "data" in campaign_data, "Should have data field"
        assert "キャンペーン名" in campaign_data["data"], "Should have campaign name"
        
        print("✓ Excel tool fallback test passed")
        return True
        
    except Exception as e:
        print(f"❌ Excel tool fallback test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("Testing Excel tool execution logic...\n")
    
    test1_success = await test_excel_tool_execution()
    test2_success = await test_excel_tool_fallback()
    
    if test1_success and test2_success:
        print("\n🎉 All Excel tool tests passed!")
        return 0
    else:
        print("\n❌ Some Excel tool tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
